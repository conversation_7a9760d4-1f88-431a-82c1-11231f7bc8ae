# Google Ads API - Basic Access Approval ✅

**Approval Date**: January 2025  
**Status**: ✅ **APPROVED AND ACTIVATED**  
**Account**: Brand Wisdom Solutions  
**Manager Account (MCC)**: 310-946-3592

---

## 🔑 API Credentials

### Developer Token (ACTIVE)
```
USJoZ_CN_pYY2MP-jIhjqA
```
> ⚠️ **IMPORTANT**: Treat this token as a sensitive password. Never commit to public repositories.

### Access Level: Basic Access
- **Daily Operations Limit**: 15,000 operations/day
- **Get Requests Limit**: 1,000 get requests/day
- **Cost**: FREE
- **Status**: Sufficient for most developers and companies

---

## 📊 Access Details

### What Basic Access Includes:
- ✅ Full Google Ads API functionality
- ✅ Access to all API services and methods
- ✅ 15,000 operations per day (sufficient for 20+ client accounts)
- ✅ Real-time data access
- ✅ Campaign creation and management
- ✅ Reporting and analytics

### Usage Recommendations:
- 🚫 Avoid using GET operations for bulk data retrieval
- ✅ Use search/query operations instead for reporting
- ✅ Implement efficient batching for mutations
- ✅ Cache frequently accessed data

### When to Apply for Standard Access:
Only apply if you consistently need more than 15,000 operations/day. Standard access requires:
- Demonstrated need through API usage patterns
- Consistent usage near or at the Basic Access limits
- Valid business justification

---

## 🔧 API Center Access

### How to View Your Token:
1. Sign in to Manager Account: **310-946-3592**
2. Navigate to: **TOOLS & SETTINGS > SETUP > API Center**
3. View your Developer Token and usage statistics

### Important to Maintain:
- 📧 **Developer Contact Email**: <EMAIL> (platform-specific communications)
- 📊 **Usage Monitoring**: Regularly check your API usage statistics
- 🔒 **Token Security**: Never share or expose your developer token
- 📨 **Manager Email**: <EMAIL> (general business)
- 🧪 **Test User**: <EMAIL> (current OAuth authorized user)

---

## 📚 Resources

### Official Documentation:
- [Google Ads API Documentation](https://developers.google.com/google-ads/api/docs/start)
- [Access Levels Guide](https://developers.google.com/google-ads/api/docs/access-levels)
- [API Terms and Conditions](https://developers.google.com/google-ads/api/docs/terms)
- [Developer Forum](https://groups.google.com/g/google-ads-api)

### Client Libraries:
- [Python](https://github.com/googleads/google-ads-python)
- [Node.js](https://github.com/googleads/google-ads-node)
- [PHP](https://github.com/googleads/google-ads-php)
- [Java](https://github.com/googleads/google-ads-java)

---

## ⚡ Quick Start Integration

### Update Your .env File:
```bash
# Google Ads API Configuration
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jIhjqA
GOOGLE_ADS_LOGIN_CUSTOMER_ID=310-946-3592  # Your MCC ID (no dashes)
GOOGLE_ADS_CLIENT_ID=<your-oauth-client-id>
GOOGLE_ADS_CLIENT_SECRET=<your-oauth-client-secret>
```

### Python Client Configuration:
```python
config = {
    "developer_token": "USJoZ_CN_pYY2MP-jIhjqA",
    "login_customer_id": "3109463592",  # Without dashes
    "client_id": "<your-oauth-client-id>",
    "client_secret": "<your-oauth-client-secret>",
    "refresh_token": "<user-refresh-token>"
}
```

---

## 🚨 Compliance Requirements

### You MUST:
- ✅ Comply with Google Ads API Terms and Conditions
- ✅ Respect rate limits and quotas
- ✅ Implement proper error handling
- ✅ Maintain data privacy and security
- ✅ Keep developer contact email updated

### You MUST NOT:
- ❌ Share or expose your developer token
- ❌ Exceed daily operation limits
- ❌ Use the API for prohibited purposes
- ❌ Attempt to circumvent rate limits

---

## 📈 Usage Monitoring

### Daily Limits:
- **Operations**: 0 / 15,000 used
- **Get Requests**: 0 / 1,000 used
- **Reset Time**: Midnight Pacific Time (PT)

### Best Practices:
1. Implement request batching
2. Cache frequently accessed data
3. Use search queries instead of get operations
4. Monitor usage through API Center
5. Set up alerts for high usage

---

## 🎯 Next Steps

1. ✅ Update `.env` with the approved developer token
2. ✅ Remove any test/demo configurations
3. ✅ Test OAuth flow with real account
4. ✅ Verify API access with sample requests
5. ✅ Implement usage monitoring
6. ✅ Begin feature development with real data

---

**Note**: This approval enables full production access to the Google Ads API. You can now build and deploy your AI-powered campaign management platform with real client data.