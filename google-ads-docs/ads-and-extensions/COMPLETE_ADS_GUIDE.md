# Complete Google Ads API - Ad Creation and Extensions Guide

## Table of Contents
1. [Overview](#overview)
2. [Ad Creation Fundamentals](#ad-creation-fundamentals)
3. [Responsive Search Ads](#responsive-search-ads)
4. [Text Ads](#text-ads)
5. [Ad Extensions](#ad-extensions)
6. [Ad Approval and Policy Compliance](#ad-approval-and-policy-compliance)
7. [Creative Asset Management](#creative-asset-management)
8. [Performance Tracking](#performance-tracking)
9. [Best Practices](#best-practices)
10. [Error Handling](#error-handling)

## Overview

The Google Ads API provides comprehensive functionality for creating, managing, and optimizing ads across all Google Ads campaign types. This guide covers ad creation, all extension types, policy compliance, and performance optimization with practical Python examples.

### Key Benefits
- **Automated Ad Creation**: Programmatically create ads at scale
- **Dynamic Extensions**: Add sitelinks, callouts, and structured snippets
- **Policy Compliance**: Handle approvals and violations automatically
- **Performance Optimization**: Track and optimize ad performance
- **Creative Management**: Manage assets and creative variations

### Prerequisites
- Google Ads API access (Basic or Standard)
- Python 3.8+ with google-ads library
- Valid Google Ads account with appropriate permissions
- Configured authentication (OAuth2 or service account)

## Ad Creation Fundamentals

### Basic Setup and Authentication

```python
#!/usr/bin/env python
# Google Ads API Ad Creation Setup

from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import sys

def initialize_client():
    """Initialize Google Ads API client."""
    try:
        client = GoogleAdsClient.load_from_storage()
        return client
    except Exception as e:
        print(f"Failed to initialize client: {e}")
        return None

def create_ad_group_ad_service(client):
    """Create ad group ad service."""
    return client.get_service("AdGroupAdService")

def create_asset_service(client):
    """Create asset service for extensions."""
    return client.get_service("AssetService")
```

### Ad Creation Workflow

```python
def create_ad_workflow(client, customer_id, ad_group_id, ad_type="responsive_search"):
    """Complete workflow for creating ads."""
    
    # Step 1: Create ad assets (headlines, descriptions)
    ad_assets = create_ad_assets(client, ad_type)
    
    # Step 2: Create the ad
    ad_operation = create_ad_operation(client, customer_id, ad_group_id, ad_assets)
    
    # Step 3: Execute the operation
    response = execute_ad_creation(client, customer_id, ad_operation)
    
    # Step 4: Handle policy compliance
    handle_policy_compliance(client, customer_id, response)
    
    return response

def create_ad_assets(client, ad_type):
    """Create ad text assets based on ad type."""
    if ad_type == "responsive_search":
        return {
            "headlines": [
                "Premium Quality Products",
                "Best Deals Online",
                "Free Shipping Available",
                "24/7 Customer Support",
                "Award-Winning Service"
            ],
            "descriptions": [
                "Discover our wide selection of premium products with competitive prices.",
                "Shop now and get free shipping on orders over $50.",
                "Join thousands of satisfied customers worldwide.",
                "Experience excellence in every purchase."
            ]
        }
    return {}
```

## Responsive Search Ads

### Creating Responsive Search Ads

```python
def create_responsive_search_ad(client, customer_id, ad_group_id, headlines, descriptions, final_urls):
    """Create a responsive search ad with multiple headlines and descriptions."""
    
    ad_group_ad_service = client.get_service("AdGroupAdService")
    
    # Create ad group ad operation
    ad_group_ad_operation = client.get_type("AdGroupAdOperation")
    ad_group_ad = ad_group_ad_operation.create
    
    # Set ad group
    ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(
        customer_id, ad_group_id
    )
    
    # Create responsive search ad info
    ad_group_ad.ad.responsive_search_ad.headlines.extend(
        [create_ad_text_asset(client, headline) for headline in headlines]
    )
    
    ad_group_ad.ad.responsive_search_ad.descriptions.extend(
        [create_ad_text_asset(client, description) for description in descriptions]
    )
    
    ad_group_ad.ad.final_urls.extend(final_urls)
    
    # Set ad status
    ad_group_ad.status = client.enums.AdGroupAdStatusEnum.PAUSED
    
    try:
        # Execute the operation
        response = ad_group_ad_service.mutate_ad_group_ads(
            customer_id=customer_id, operations=[ad_group_ad_operation]
        )
        
        print(f"Created responsive search ad: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        handle_policy_violations(ex)
        return None

def create_ad_text_asset(client, text, pinned_field=None):
    """Create an ad text asset with optional pinning."""
    ad_text_asset = client.get_type("AdTextAsset")
    ad_text_asset.text = text
    
    if pinned_field:
        ad_text_asset.pinned_field = pinned_field
    
    return ad_text_asset
```

### Advanced Responsive Search Ad Features

```python
def create_advanced_responsive_search_ad(client, customer_id, ad_group_id):
    """Create responsive search ad with pinning and advanced features."""
    
    # Headlines with pinning
    headlines = [
        {"text": "Premium Brand Store", "pin": client.enums.ServedAssetFieldTypeEnum.HEADLINE_1},
        {"text": "Best Prices Guaranteed", "pin": None},
        {"text": "Free Shipping Today", "pin": None},
        {"text": "Award-Winning Service", "pin": client.enums.ServedAssetFieldTypeEnum.HEADLINE_2},
        {"text": "Shop Now and Save", "pin": None}
    ]
    
    # Descriptions with pinning
    descriptions = [
        {"text": "Discover our premium collection with unbeatable prices and quality.", "pin": None},
        {"text": "Free shipping on all orders. 30-day money-back guarantee.", "pin": client.enums.ServedAssetFieldTypeEnum.DESCRIPTION_1},
        {"text": "Join over 100K satisfied customers. Shop with confidence today.", "pin": None}
    ]
    
    ad_group_ad_service = client.get_service("AdGroupAdService")
    ad_group_ad_operation = client.get_type("AdGroupAdOperation")
    ad_group_ad = ad_group_ad_operation.create
    
    # Set ad group
    ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(
        customer_id, ad_group_id
    )
    
    # Create headlines with pinning
    for headline in headlines:
        ad_text_asset = create_ad_text_asset(client, headline["text"], headline["pin"])
        ad_group_ad.ad.responsive_search_ad.headlines.append(ad_text_asset)
    
    # Create descriptions with pinning
    for description in descriptions:
        ad_text_asset = create_ad_text_asset(client, description["text"], description["pin"])
        ad_group_ad.ad.responsive_search_ad.descriptions.append(ad_text_asset)
    
    # Set final URLs
    ad_group_ad.ad.final_urls.extend([
        "https://example.com/landing-page",
        "https://example.com/mobile-landing-page"
    ])
    
    # Set path fields
    ad_group_ad.ad.responsive_search_ad.path1 = "shop"
    ad_group_ad.ad.responsive_search_ad.path2 = "deals"
    
    # Set status
    ad_group_ad.status = client.enums.AdGroupAdStatusEnum.PAUSED
    
    try:
        response = ad_group_ad_service.mutate_ad_group_ads(
            customer_id=customer_id, operations=[ad_group_ad_operation]
        )
        
        print(f"Created advanced responsive search ad: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        handle_policy_violations(ex)
        return None
```

## Text Ads

### Creating Expanded Text Ads

```python
def create_expanded_text_ad(client, customer_id, ad_group_id):
    """Create an expanded text ad (legacy format)."""
    
    ad_group_ad_service = client.get_service("AdGroupAdService")
    ad_group_ad_operation = client.get_type("AdGroupAdOperation")
    ad_group_ad = ad_group_ad_operation.create
    
    # Set ad group
    ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(
        customer_id, ad_group_id
    )
    
    # Create expanded text ad
    expanded_text_ad = ad_group_ad.ad.expanded_text_ad
    expanded_text_ad.headline_part1 = "Premium Quality Products"
    expanded_text_ad.headline_part2 = "Best Deals Online"
    expanded_text_ad.description = "Shop our premium collection with competitive prices and free shipping."
    expanded_text_ad.path1 = "shop"
    expanded_text_ad.path2 = "deals"
    
    # Set final URLs
    ad_group_ad.ad.final_urls.extend(["https://example.com/products"])
    
    # Set status
    ad_group_ad.status = client.enums.AdGroupAdStatusEnum.PAUSED
    
    try:
        response = ad_group_ad_service.mutate_ad_group_ads(
            customer_id=customer_id, operations=[ad_group_ad_operation]
        )
        
        print(f"Created expanded text ad: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        print(f"Error creating expanded text ad: {ex}")
        return None
```

## Ad Extensions

### Sitelink Extensions

```python
def create_sitelink_assets(client, customer_id, campaign_id):
    """Create sitelink assets and associate with campaign."""
    
    asset_service = client.get_service("AssetService")
    
    # Sitelink data
    sitelinks_data = [
        {
            "link_text": "Store Hours",
            "description1": "Find our hours",
            "description2": "Open 24/7",
            "final_urls": ["https://example.com/hours"]
        },
        {
            "link_text": "Contact Us",
            "description1": "Get in touch",
            "description2": "Customer service",
            "final_urls": ["https://example.com/contact"]
        },
        {
            "link_text": "About Us",
            "description1": "Learn more",
            "description2": "Our story",
            "final_urls": ["https://example.com/about"]
        },
        {
            "link_text": "Special Offers",
            "description1": "Limited time",
            "description2": "Save up to 50%",
            "final_urls": ["https://example.com/offers"]
        }
    ]
    
    # Create sitelink operations
    sitelink_operations = []
    
    for sitelink_data in sitelinks_data:
        asset_operation = client.get_type("AssetOperation")
        asset = asset_operation.create
        
        # Set sitelink asset properties
        asset.sitelink_asset.link_text = sitelink_data["link_text"]
        asset.sitelink_asset.description1 = sitelink_data["description1"]
        asset.sitelink_asset.description2 = sitelink_data["description2"]
        asset.sitelink_asset.final_urls.extend(sitelink_data["final_urls"])
        
        sitelink_operations.append(asset_operation)
    
    # Execute sitelink creation
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=sitelink_operations
        )
        
        print(f"Created {len(response.results)} sitelink assets")
        
        # Associate with campaign
        associate_assets_with_campaign(
            client, customer_id, campaign_id, 
            [result.resource_name for result in response.results],
            client.enums.AssetFieldTypeEnum.SITELINK
        )
        
        return response.results
        
    except GoogleAdsException as ex:
        print(f"Error creating sitelinks: {ex}")
        return None
```

### Callout Extensions

```python
def create_callout_assets(client, customer_id, campaign_id):
    """Create callout assets and associate with campaign."""
    
    asset_service = client.get_service("AssetService")
    
    # Callout texts (max 25 characters each)
    callout_texts = [
        "Free Shipping",
        "24/7 Support",
        "Best Price Guarantee",
        "Award Winning Service",
        "30-Day Returns",
        "Expert Advice",
        "Quick Delivery",
        "Secure Checkout"
    ]
    
    # Create callout operations
    callout_operations = []
    
    for callout_text in callout_texts:
        asset_operation = client.get_type("AssetOperation")
        asset = asset_operation.create
        asset.callout_asset.callout_text = callout_text
        
        callout_operations.append(asset_operation)
    
    # Execute callout creation
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=callout_operations
        )
        
        print(f"Created {len(response.results)} callout assets")
        
        # Associate with campaign
        associate_assets_with_campaign(
            client, customer_id, campaign_id,
            [result.resource_name for result in response.results],
            client.enums.AssetFieldTypeEnum.CALLOUT
        )
        
        return response.results
        
    except GoogleAdsException as ex:
        print(f"Error creating callouts: {ex}")
        return None
```

### Structured Snippet Extensions

```python
def create_structured_snippet_assets(client, customer_id, campaign_id):
    """Create structured snippet assets and associate with campaign."""
    
    asset_service = client.get_service("AssetService")
    
    # Structured snippets data
    structured_snippets_data = [
        {
            "header": "BRANDS",
            "values": ["Nike", "Adidas", "Puma", "Reebok", "Under Armour"]
        },
        {
            "header": "SERVICES",
            "values": ["Installation", "Repair", "Maintenance", "Consultation"]
        },
        {
            "header": "TYPES",
            "values": ["Running", "Basketball", "Football", "Tennis", "Training"]
        },
        {
            "header": "STYLES",
            "values": ["Casual", "Formal", "Athletic", "Outdoor", "Fashion"]
        }
    ]
    
    # Create structured snippet operations
    structured_snippet_operations = []
    
    for snippet_data in structured_snippets_data:
        asset_operation = client.get_type("AssetOperation")
        asset = asset_operation.create
        
        # Set structured snippet properties
        asset.structured_snippet_asset.header = snippet_data["header"]
        asset.structured_snippet_asset.values.extend(snippet_data["values"])
        
        structured_snippet_operations.append(asset_operation)
    
    # Execute structured snippet creation
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=structured_snippet_operations
        )
        
        print(f"Created {len(response.results)} structured snippet assets")
        
        # Associate with campaign
        associate_assets_with_campaign(
            client, customer_id, campaign_id,
            [result.resource_name for result in response.results],
            client.enums.AssetFieldTypeEnum.STRUCTURED_SNIPPET
        )
        
        return response.results
        
    except GoogleAdsException as ex:
        print(f"Error creating structured snippets: {ex}")
        return None
```

### Price Extensions

```python
def create_price_assets(client, customer_id, campaign_id):
    """Create price assets and associate with campaign."""
    
    asset_service = client.get_service("AssetService")
    
    # Price offerings data
    price_offerings_data = [
        {
            "header": "Premium Package",
            "description": "Full service solution",
            "price": 99.99,
            "unit": "USD",
            "final_urls": ["https://example.com/premium"]
        },
        {
            "header": "Standard Package",
            "description": "Essential features",
            "price": 49.99,
            "unit": "USD",
            "final_urls": ["https://example.com/standard"]
        },
        {
            "header": "Basic Package",
            "description": "Get started",
            "price": 19.99,
            "unit": "USD",
            "final_urls": ["https://example.com/basic"]
        }
    ]
    
    # Create price asset operation
    asset_operation = client.get_type("AssetOperation")
    asset = asset_operation.create
    
    # Set price asset properties
    asset.price_asset.type_ = client.enums.PriceExtensionTypeEnum.SERVICES
    asset.price_asset.price_qualifier = client.enums.PriceExtensionPriceQualifierEnum.FROM
    asset.price_asset.language_code = "en"
    
    # Add price offerings
    for offering_data in price_offerings_data:
        price_offering = client.get_type("PriceOffering")
        price_offering.header = offering_data["header"]
        price_offering.description = offering_data["description"]
        
        # Set price
        money = client.get_type("Money")
        money.currency_code = offering_data["unit"]
        money.amount_micros = int(offering_data["price"] * 1000000)
        price_offering.price = money
        
        price_offering.final_urls.extend(offering_data["final_urls"])
        
        asset.price_asset.price_offerings.append(price_offering)
    
    # Execute price asset creation
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=[asset_operation]
        )
        
        print(f"Created price asset: {response.results[0].resource_name}")
        
        # Associate with campaign
        associate_assets_with_campaign(
            client, customer_id, campaign_id,
            [response.results[0].resource_name],
            client.enums.AssetFieldTypeEnum.PRICE
        )
        
        return response.results[0]
        
    except GoogleAdsException as ex:
        print(f"Error creating price asset: {ex}")
        return None
```

### Promotion Extensions

```python
def create_promotion_assets(client, customer_id, campaign_id):
    """Create promotion assets and associate with campaign."""
    
    asset_service = client.get_service("AssetService")
    
    # Promotion data
    promotions_data = [
        {
            "promotion_target": "Black Friday Sale",
            "discount_modifier": "UP_TO",
            "percent_off": 50,
            "occasion": "BLACK_FRIDAY",
            "language_code": "en",
            "final_urls": ["https://example.com/black-friday"]
        },
        {
            "promotion_target": "New Customer Discount",
            "discount_modifier": "EXACTLY",
            "percent_off": 20,
            "occasion": "UNKNOWN",
            "language_code": "en",
            "final_urls": ["https://example.com/new-customer"]
        }
    ]
    
    promotion_operations = []
    
    for promotion_data in promotions_data:
        asset_operation = client.get_type("AssetOperation")
        asset = asset_operation.create
        
        # Set promotion asset properties
        asset.promotion_asset.promotion_target = promotion_data["promotion_target"]
        asset.promotion_asset.discount_modifier = getattr(
            client.enums.PromotionExtensionDiscountModifierEnum,
            promotion_data["discount_modifier"]
        )
        asset.promotion_asset.percent_off = promotion_data["percent_off"]
        asset.promotion_asset.occasion = getattr(
            client.enums.PromotionExtensionOccasionEnum,
            promotion_data["occasion"]
        )
        asset.promotion_asset.language_code = promotion_data["language_code"]
        asset.promotion_asset.final_urls.extend(promotion_data["final_urls"])
        
        promotion_operations.append(asset_operation)
    
    # Execute promotion creation
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=promotion_operations
        )
        
        print(f"Created {len(response.results)} promotion assets")
        
        # Associate with campaign
        associate_assets_with_campaign(
            client, customer_id, campaign_id,
            [result.resource_name for result in response.results],
            client.enums.AssetFieldTypeEnum.PROMOTION
        )
        
        return response.results
        
    except GoogleAdsException as ex:
        print(f"Error creating promotions: {ex}")
        return None
```

### Asset Association Helper

```python
def associate_assets_with_campaign(client, customer_id, campaign_id, asset_resource_names, field_type):
    """Associate assets with a campaign."""
    
    campaign_asset_service = client.get_service("CampaignAssetService")
    campaign_asset_operations = []
    
    for asset_resource_name in asset_resource_names:
        campaign_asset_operation = client.get_type("CampaignAssetOperation")
        campaign_asset = campaign_asset_operation.create
        
        campaign_asset.asset = asset_resource_name
        campaign_asset.campaign = client.get_service("CampaignService").campaign_path(
            customer_id, campaign_id
        )
        campaign_asset.field_type = field_type
        
        campaign_asset_operations.append(campaign_asset_operation)
    
    # Execute campaign asset operations
    try:
        response = campaign_asset_service.mutate_campaign_assets(
            customer_id=customer_id, operations=campaign_asset_operations
        )
        
        print(f"Associated {len(response.results)} assets with campaign")
        return response.results
        
    except GoogleAdsException as ex:
        print(f"Error associating assets: {ex}")
        return None
```

## Ad Approval and Policy Compliance

### Tracking Ad Approval Status

```python
def get_ad_approval_status(client, customer_id, campaign_id=None):
    """Get approval status for all ads or specific campaign ads."""
    
    ga_service = client.get_service("GoogleAdsService")
    
    # Build query
    query = """
        SELECT 
            ad_group_ad.ad.id,
            ad_group_ad.ad.type,
            ad_group_ad.policy_summary.approval_status,
            ad_group_ad.policy_summary.review_status,
            ad_group_ad.policy_summary.policy_topic_entries,
            campaign.id,
            campaign.name,
            ad_group.id,
            ad_group.name
        FROM ad_group_ad 
        WHERE ad_group_ad.status != 'REMOVED'
    """
    
    if campaign_id:
        query += f" AND campaign.id = {campaign_id}"
    
    try:
        # Execute query
        response = ga_service.search_stream(customer_id=customer_id, query=query)
        
        approval_status_report = []
        
        for batch in response:
            for row in batch.results:
                ad_info = {
                    "ad_id": row.ad_group_ad.ad.id,
                    "ad_type": row.ad_group_ad.ad.type_.name,
                    "approval_status": row.ad_group_ad.policy_summary.approval_status.name,
                    "review_status": row.ad_group_ad.policy_summary.review_status.name,
                    "campaign_id": row.campaign.id,
                    "campaign_name": row.campaign.name,
                    "ad_group_id": row.ad_group.id,
                    "ad_group_name": row.ad_group.name,
                    "policy_violations": []
                }
                
                # Extract policy violations
                for policy_topic_entry in row.ad_group_ad.policy_summary.policy_topic_entries:
                    violation = {
                        "topic": policy_topic_entry.topic,
                        "type": policy_topic_entry.type_.name,
                        "evidences": [evidence.text_list.texts for evidence in policy_topic_entry.evidences]
                    }
                    ad_info["policy_violations"].append(violation)
                
                approval_status_report.append(ad_info)
        
        return approval_status_report
        
    except GoogleAdsException as ex:
        print(f"Error getting approval status: {ex}")
        return None
```

### Get All Disapproved Ads

```python
def get_disapproved_ads(client, customer_id, campaign_id=None):
    """Get all disapproved ads with policy violation details."""
    
    ga_service = client.get_service("GoogleAdsService")
    
    # Query for disapproved ads
    query = """
        SELECT 
            ad_group_ad.ad.id,
            ad_group_ad.ad.type,
            ad_group_ad.policy_summary.approval_status,
            ad_group_ad.policy_summary.policy_topic_entries,
            campaign.id,
            campaign.name,
            ad_group.id,
            ad_group.name
        FROM ad_group_ad 
        WHERE ad_group_ad.policy_summary.approval_status = DISAPPROVED
    """
    
    if campaign_id:
        query += f" AND campaign.id = {campaign_id}"
    
    try:
        response = ga_service.search_stream(customer_id=customer_id, query=query)
        
        disapproved_ads = []
        
        for batch in response:
            for row in batch.results:
                ad_info = {
                    "ad_id": row.ad_group_ad.ad.id,
                    "ad_type": row.ad_group_ad.ad.type_.name,
                    "campaign_name": row.campaign.name,
                    "ad_group_name": row.ad_group.name,
                    "disapproval_reasons": []
                }
                
                # Extract disapproval reasons
                for policy_topic_entry in row.ad_group_ad.policy_summary.policy_topic_entries:
                    reason = {
                        "topic": policy_topic_entry.topic,
                        "type": policy_topic_entry.type_.name,
                        "evidences": []
                    }
                    
                    for evidence in policy_topic_entry.evidences:
                        if evidence.text_list:
                            reason["evidences"].extend(evidence.text_list.texts)
                    
                    ad_info["disapproval_reasons"].append(reason)
                
                disapproved_ads.append(ad_info)
        
        return disapproved_ads
        
    except GoogleAdsException as ex:
        print(f"Error getting disapproved ads: {ex}")
        return None
```

### Handle Policy Violations

```python
def handle_policy_violations(exception):
    """Handle policy violations from ad creation attempts."""
    
    if not isinstance(exception, GoogleAdsException):
        return
    
    policy_violations = []
    ignorable_violations = []
    
    for error in exception.failure.errors:
        if error.error_code.policy_violation_error:
            # Extract policy violation details
            violation_details = {
                "error_code": error.error_code.policy_violation_error.name,
                "message": error.message,
                "trigger": error.trigger,
                "location": error.location
            }
            
            # Check if violation is ignorable
            if hasattr(error.details, 'policy_violation_details'):
                violation_details["is_ignorable"] = error.details.policy_violation_details.is_ignorable
                violation_details["external_policy_name"] = error.details.policy_violation_details.external_policy_name
                
                if error.details.policy_violation_details.is_ignorable:
                    ignorable_violations.append(violation_details)
                else:
                    policy_violations.append(violation_details)
            else:
                policy_violations.append(violation_details)
    
    # Print violation details
    if policy_violations:
        print("Non-ignorable policy violations:")
        for violation in policy_violations:
            print(f"  - {violation['error_code']}: {violation['message']}")
    
    if ignorable_violations:
        print("Ignorable policy violations:")
        for violation in ignorable_violations:
            print(f"  - {violation['error_code']}: {violation['message']}")
        
        # Can request exemption for ignorable violations
        return request_policy_exemption(ignorable_violations)
    
    return None

def request_policy_exemption(violations):
    """Request exemption for ignorable policy violations."""
    
    # Extract ignorable policy topics
    ignorable_topics = []
    for violation in violations:
        if violation.get("is_ignorable"):
            ignorable_topics.append(violation["external_policy_name"])
    
    print(f"Requesting exemption for {len(ignorable_topics)} policy topics")
    
    # Return exemption request data
    return {
        "ignorable_policy_topics": ignorable_topics,
        "exemption_requested": True
    }
```

### Create Ad with Policy Exemption

```python
def create_ad_with_exemption(client, customer_id, ad_group_id, ad_data, ignorable_policy_topics=None):
    """Create ad with policy exemption for ignorable violations."""
    
    ad_group_ad_service = client.get_service("AdGroupAdService")
    ad_group_ad_operation = client.get_type("AdGroupAdOperation")
    ad_group_ad = ad_group_ad_operation.create
    
    # Build ad (example for responsive search ad)
    ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(
        customer_id, ad_group_id
    )
    
    # Set ad content
    ad_group_ad.ad.responsive_search_ad.headlines.extend(
        [create_ad_text_asset(client, headline) for headline in ad_data["headlines"]]
    )
    ad_group_ad.ad.responsive_search_ad.descriptions.extend(
        [create_ad_text_asset(client, description) for description in ad_data["descriptions"]]
    )
    ad_group_ad.ad.final_urls.extend(ad_data["final_urls"])
    
    # Set policy exemption if provided
    if ignorable_policy_topics:
        for topic in ignorable_policy_topics:
            exemption_request = client.get_type("PolicyViolationKey")
            exemption_request.policy_name = topic
            ad_group_ad_operation.exemption_request.policy_violation_keys.append(exemption_request)
    
    # Set status
    ad_group_ad.status = client.enums.AdGroupAdStatusEnum.PAUSED
    
    try:
        response = ad_group_ad_service.mutate_ad_group_ads(
            customer_id=customer_id, operations=[ad_group_ad_operation]
        )
        
        print(f"Created ad with exemption: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        print(f"Error creating ad with exemption: {ex}")
        return handle_policy_violations(ex)
```

## Creative Asset Management

### Upload Image Assets

```python
def upload_image_asset(client, customer_id, image_path, asset_name):
    """Upload image asset for display ads."""
    
    asset_service = client.get_service("AssetService")
    
    # Read image file
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()
    
    # Create asset operation
    asset_operation = client.get_type("AssetOperation")
    asset = asset_operation.create
    
    # Set image asset properties
    asset.image_asset.data = image_data
    asset.image_asset.mime_type = determine_mime_type(image_path)
    asset.name = asset_name
    
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=[asset_operation]
        )
        
        print(f"Uploaded image asset: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        print(f"Error uploading image asset: {ex}")
        return None

def determine_mime_type(file_path):
    """Determine MIME type from file extension."""
    
    extension = file_path.lower().split('.')[-1]
    
    mime_types = {
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'png': 'image/png',
        'gif': 'image/gif',
        'svg': 'image/svg+xml'
    }
    
    return mime_types.get(extension, 'image/jpeg')
```

### Create YouTube Video Assets

```python
def create_youtube_video_asset(client, customer_id, youtube_video_id, asset_name):
    """Create YouTube video asset for video ads."""
    
    asset_service = client.get_service("AssetService")
    
    # Create asset operation
    asset_operation = client.get_type("AssetOperation")
    asset = asset_operation.create
    
    # Set YouTube video asset properties
    asset.youtube_video_asset.youtube_video_id = youtube_video_id
    asset.name = asset_name
    
    try:
        response = asset_service.mutate_assets(
            customer_id=customer_id, operations=[asset_operation]
        )
        
        print(f"Created YouTube video asset: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        print(f"Error creating YouTube video asset: {ex}")
        return None
```

### Manage Asset Libraries

```python
def get_all_assets(client, customer_id, asset_type=None):
    """Get all assets from account."""
    
    ga_service = client.get_service("GoogleAdsService")
    
    # Build query
    query = """
        SELECT 
            asset.id,
            asset.name,
            asset.type,
            asset.resource_name,
            asset.image_asset.file_size,
            asset.image_asset.mime_type,
            asset.image_asset.full_size.width_pixels,
            asset.image_asset.full_size.height_pixels,
            asset.youtube_video_asset.youtube_video_id,
            asset.youtube_video_asset.youtube_video_title
        FROM asset 
        WHERE asset.type != 'UNKNOWN'
    """
    
    if asset_type:
        query += f" AND asset.type = '{asset_type}'"
    
    try:
        response = ga_service.search_stream(customer_id=customer_id, query=query)
        
        assets = []
        
        for batch in response:
            for row in batch.results:
                asset_info = {
                    "id": row.asset.id,
                    "name": row.asset.name,
                    "type": row.asset.type.name,
                    "resource_name": row.asset.resource_name
                }
                
                # Add type-specific information
                if row.asset.type.name == "IMAGE":
                    asset_info.update({
                        "file_size": row.asset.image_asset.file_size,
                        "mime_type": row.asset.image_asset.mime_type.name,
                        "width": row.asset.image_asset.full_size.width_pixels,
                        "height": row.asset.image_asset.full_size.height_pixels
                    })
                elif row.asset.type.name == "YOUTUBE_VIDEO":
                    asset_info.update({
                        "youtube_video_id": row.asset.youtube_video_asset.youtube_video_id,
                        "youtube_video_title": row.asset.youtube_video_asset.youtube_video_title
                    })
                
                assets.append(asset_info)
        
        return assets
        
    except GoogleAdsException as ex:
        print(f"Error getting assets: {ex}")
        return None
```

## Performance Tracking

### Ad Performance Metrics

```python
def get_ad_performance_metrics(client, customer_id, date_range="LAST_30_DAYS"):
    """Get comprehensive ad performance metrics."""
    
    ga_service = client.get_service("GoogleAdsService")
    
    # Performance metrics query
    query = f"""
        SELECT 
            ad_group_ad.ad.id,
            ad_group_ad.ad.type,
            campaign.id,
            campaign.name,
            ad_group.id,
            ad_group.name,
            metrics.impressions,
            metrics.clicks,
            metrics.ctr,
            metrics.average_cpc,
            metrics.cost_micros,
            metrics.conversions,
            metrics.conversion_rate,
            metrics.cost_per_conversion,
            metrics.quality_score,
            metrics.search_impression_share,
            metrics.search_rank_lost_impression_share,
            metrics.search_budget_lost_impression_share
        FROM ad_group_ad 
        WHERE segments.date DURING {date_range}
            AND ad_group_ad.status != 'REMOVED'
        ORDER BY metrics.impressions DESC
    """
    
    try:
        response = ga_service.search_stream(customer_id=customer_id, query=query)
        
        performance_data = []
        
        for batch in response:
            for row in batch.results:
                performance_info = {
                    "ad_id": row.ad_group_ad.ad.id,
                    "ad_type": row.ad_group_ad.ad.type_.name,
                    "campaign_id": row.campaign.id,
                    "campaign_name": row.campaign.name,
                    "ad_group_id": row.ad_group.id,
                    "ad_group_name": row.ad_group.name,
                    "impressions": row.metrics.impressions,
                    "clicks": row.metrics.clicks,
                    "ctr": row.metrics.ctr,
                    "average_cpc": row.metrics.average_cpc,
                    "cost": row.metrics.cost_micros / 1000000,  # Convert to currency
                    "conversions": row.metrics.conversions,
                    "conversion_rate": row.metrics.conversion_rate,
                    "cost_per_conversion": row.metrics.cost_per_conversion,
                    "quality_score": row.metrics.quality_score,
                    "search_impression_share": row.metrics.search_impression_share,
                    "search_rank_lost_impression_share": row.metrics.search_rank_lost_impression_share,
                    "search_budget_lost_impression_share": row.metrics.search_budget_lost_impression_share
                }
                
                performance_data.append(performance_info)
        
        return performance_data
        
    except GoogleAdsException as ex:
        print(f"Error getting ad performance: {ex}")
        return None
```

### Extension Performance Tracking

```python
def get_extension_performance(client, customer_id, date_range="LAST_30_DAYS"):
    """Get performance metrics for ad extensions."""
    
    ga_service = client.get_service("GoogleAdsService")
    
    # Extension performance query
    query = f"""
        SELECT 
            campaign.id,
            campaign.name,
            campaign_asset.asset,
            campaign_asset.field_type,
            campaign_asset.status,
            metrics.impressions,
            metrics.clicks,
            metrics.ctr,
            metrics.cost_micros
        FROM campaign_asset 
        WHERE segments.date DURING {date_range}
            AND campaign_asset.status = 'ENABLED'
        ORDER BY metrics.impressions DESC
    """
    
    try:
        response = ga_service.search_stream(customer_id=customer_id, query=query)
        
        extension_performance = []
        
        for batch in response:
            for row in batch.results:
                extension_info = {
                    "campaign_id": row.campaign.id,
                    "campaign_name": row.campaign.name,
                    "asset_resource_name": row.campaign_asset.asset,
                    "extension_type": row.campaign_asset.field_type.name,
                    "status": row.campaign_asset.status.name,
                    "impressions": row.metrics.impressions,
                    "clicks": row.metrics.clicks,
                    "ctr": row.metrics.ctr,
                    "cost": row.metrics.cost_micros / 1000000
                }
                
                extension_performance.append(extension_info)
        
        return extension_performance
        
    except GoogleAdsException as ex:
        print(f"Error getting extension performance: {ex}")
        return None
```

## Best Practices

### Ad Creation Best Practices

```python
def create_optimized_responsive_search_ad(client, customer_id, ad_group_id, business_info):
    """Create optimized responsive search ad following best practices."""
    
    # Best practice: Use 8-15 headlines with variety
    headlines = [
        f"{business_info['brand_name']} - {business_info['main_service']}",  # Brand + service
        f"Best {business_info['main_service']} in {business_info['location']}",  # Location-specific
        f"{business_info['unique_selling_point']}",  # USP
        f"Free {business_info['free_offer']} - Limited Time",  # Urgency
        f"{business_info['experience_years']}+ Years Experience",  # Trust signal
        f"Call {business_info['phone']} Now",  # Call to action
        f"Award-Winning {business_info['main_service']}",  # Social proof
        f"Get Quote Today - {business_info['brand_name']}",  # Action-oriented
        f"Professional {business_info['main_service']} Service",  # Professional
        f"Trusted by {business_info['customers_served']}+ Customers",  # Trust
        f"Same Day {business_info['main_service']}",  # Speed
        f"Licensed & Insured {business_info['main_service']}"  # Trust
    ]
    
    # Best practice: Use 2-4 descriptions with different angles
    descriptions = [
        f"Get professional {business_info['main_service']} from {business_info['brand_name']}. "
        f"With {business_info['experience_years']}+ years experience, we deliver quality results. "
        f"Free estimates available.",
        
        f"Why choose {business_info['brand_name']}? Licensed, insured, and locally owned. "
        f"We've served {business_info['customers_served']}+ satisfied customers in {business_info['location']}. "
        f"Call today for your free consultation.",
        
        f"Looking for reliable {business_info['main_service']}? Our expert team provides "
        f"fast, affordable solutions. {business_info['guarantee']} guarantee on all work.",
        
        f"Transform your space with {business_info['brand_name']}. We offer competitive pricing, "
        f"quality materials, and exceptional service. Contact us for a free quote today."
    ]
    
    # Create ad with best practices
    ad_group_ad_service = client.get_service("AdGroupAdService")
    ad_group_ad_operation = client.get_type("AdGroupAdOperation")
    ad_group_ad = ad_group_ad_operation.create
    
    # Set ad group
    ad_group_ad.ad_group = client.get_service("AdGroupService").ad_group_path(
        customer_id, ad_group_id
    )
    
    # Add headlines with strategic pinning
    for i, headline in enumerate(headlines):
        ad_text_asset = create_ad_text_asset(client, headline)
        
        # Pin brand headline to position 1
        if i == 0:
            ad_text_asset.pinned_field = client.enums.ServedAssetFieldTypeEnum.HEADLINE_1
        
        ad_group_ad.ad.responsive_search_ad.headlines.append(ad_text_asset)
    
    # Add descriptions
    for description in descriptions:
        ad_text_asset = create_ad_text_asset(client, description)
        ad_group_ad.ad.responsive_search_ad.descriptions.append(ad_text_asset)
    
    # Set final URLs
    ad_group_ad.ad.final_urls.extend([
        business_info['website'],
        business_info['mobile_website']
    ])
    
    # Set path fields for better URL display
    ad_group_ad.ad.responsive_search_ad.path1 = business_info['service_path']
    ad_group_ad.ad.responsive_search_ad.path2 = business_info['location_path']
    
    # Set status
    ad_group_ad.status = client.enums.AdGroupAdStatusEnum.PAUSED
    
    try:
        response = ad_group_ad_service.mutate_ad_group_ads(
            customer_id=customer_id, operations=[ad_group_ad_operation]
        )
        
        print(f"Created optimized responsive search ad: {response.results[0].resource_name}")
        return response.results[0]
        
    except GoogleAdsException as ex:
        return handle_policy_violations(ex)
```

### Extension Optimization

```python
def optimize_extensions_for_campaign(client, customer_id, campaign_id, business_info):
    """Create optimized extensions for maximum performance."""
    
    # Create comprehensive sitelinks
    sitelinks = create_strategic_sitelinks(client, customer_id, campaign_id, business_info)
    
    # Create compelling callouts
    callouts = create_compelling_callouts(client, customer_id, campaign_id, business_info)
    
    # Create relevant structured snippets
    structured_snippets = create_relevant_structured_snippets(client, customer_id, campaign_id, business_info)
    
    # Create price extensions if applicable
    if business_info.get('has_pricing'):
        price_extensions = create_price_extensions(client, customer_id, campaign_id, business_info)
    
    # Create promotion extensions for special offers
    if business_info.get('has_promotions'):
        promotion_extensions = create_promotion_extensions(client, customer_id, campaign_id, business_info)
    
    print("All extensions created and optimized for maximum performance")
    
    return {
        'sitelinks': sitelinks,
        'callouts': callouts,
        'structured_snippets': structured_snippets,
        'price_extensions': price_extensions if business_info.get('has_pricing') else None,
        'promotion_extensions': promotion_extensions if business_info.get('has_promotions') else None
    }

def create_strategic_sitelinks(client, customer_id, campaign_id, business_info):
    """Create strategic sitelinks based on business type."""
    
    # Service-based sitelinks
    service_sitelinks = [
        {
            "link_text": "Services",
            "description1": "Full service list",
            "description2": "Expert solutions",
            "final_urls": [f"{business_info['website']}/services"]
        },
        {
            "link_text": "About Us",
            "description1": f"{business_info['experience_years']}+ years",
            "description2": "Our story",
            "final_urls": [f"{business_info['website']}/about"]
        },
        {
            "link_text": "Contact",
            "description1": "Get in touch",
            "description2": "Free consultation",
            "final_urls": [f"{business_info['website']}/contact"]
        },
        {
            "link_text": "Reviews",
            "description1": "Customer feedback",
            "description2": "5-star rated",
            "final_urls": [f"{business_info['website']}/reviews"]
        }
    ]
    
    return create_sitelink_assets(client, customer_id, campaign_id, service_sitelinks)

def create_compelling_callouts(client, customer_id, campaign_id, business_info):
    """Create compelling callouts that highlight unique value propositions."""
    
    # Dynamic callouts based on business type
    callouts = [
        f"Free {business_info['free_offer']}",
        f"{business_info['experience_years']}+ Years",
        "Licensed & Insured",
        "Same Day Service",
        "100% Satisfaction",
        "Local Family Owned",
        "Award Winning",
        "24/7 Emergency"
    ]
    
    return create_callout_assets(client, customer_id, campaign_id, callouts)
```

## Error Handling

### Comprehensive Error Handling

```python
def handle_ad_creation_errors(func):
    """Decorator for comprehensive error handling in ad creation."""
    
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except GoogleAdsException as ex:
            print(f"Google Ads API error in {func.__name__}:")
            
            for error in ex.failure.errors:
                print(f"  Error: {error.error_code}")
                print(f"  Message: {error.message}")
                
                if error.location:
                    for field_path_element in error.location.field_path_elements:
                        print(f"  Field: {field_path_element.field_name}")
                
                # Handle specific error types
                if error.error_code.policy_violation_error:
                    handle_policy_error(error)
                elif error.error_code.quota_error:
                    handle_quota_error(error)
                elif error.error_code.authorization_error:
                    handle_authorization_error(error)
                elif error.error_code.request_error:
                    handle_request_error(error)
            
            return None
        except Exception as ex:
            print(f"Unexpected error in {func.__name__}: {ex}")
            return None
    
    return wrapper

def handle_policy_error(error):
    """Handle policy-related errors."""
    print(f"  Policy violation: {error.error_code.policy_violation_error.name}")
    
    if hasattr(error.details, 'policy_violation_details'):
        details = error.details.policy_violation_details
        print(f"  External policy: {details.external_policy_name}")
        print(f"  Is ignorable: {details.is_ignorable}")
        
        if details.is_ignorable:
            print("  -> Can request exemption for this violation")

def handle_quota_error(error):
    """Handle quota-related errors."""
    print(f"  Quota error: {error.error_code.quota_error.name}")
    print("  -> Check your API quota limits and usage")

def handle_authorization_error(error):
    """Handle authorization errors."""
    print(f"  Authorization error: {error.error_code.authorization_error.name}")
    print("  -> Check your credentials and permissions")

def handle_request_error(error):
    """Handle request-related errors."""
    print(f"  Request error: {error.error_code.request_error.name}")
    print("  -> Check your request parameters and format")
```

### Retry Logic for API Calls

```python
import time
import random

def retry_api_call(func, max_retries=3, backoff_factor=2):
    """Retry API calls with exponential backoff."""
    
    for attempt in range(max_retries):
        try:
            return func()
        except GoogleAdsException as ex:
            # Check if error is retryable
            if is_retryable_error(ex) and attempt < max_retries - 1:
                wait_time = backoff_factor ** attempt + random.uniform(0, 1)
                print(f"Retrying after {wait_time:.2f} seconds (attempt {attempt + 1}/{max_retries})")
                time.sleep(wait_time)
            else:
                raise ex
    
    return None

def is_retryable_error(exception):
    """Check if error is retryable (rate limiting, temporary issues)."""
    
    retryable_errors = [
        'RATE_EXCEEDED',
        'QUOTA_EXCEEDED',
        'INTERNAL_ERROR',
        'CONCURRENT_MODIFICATION'
    ]
    
    for error in exception.failure.errors:
        error_name = str(error.error_code)
        if any(retryable in error_name for retryable in retryable_errors):
            return True
    
    return False
```

## Complete Example: AI-Powered Ad Copy Laboratory

```python
class AdCopyLaboratory:
    """AI-powered ad copy creation and optimization laboratory."""
    
    def __init__(self, client, customer_id):
        self.client = client
        self.customer_id = customer_id
        self.ad_group_ad_service = client.get_service("AdGroupAdService")
        self.asset_service = client.get_service("AssetService")
    
    @handle_ad_creation_errors
    def create_ad_variations(self, ad_group_id, business_info, num_variations=3):
        """Create multiple ad variations for A/B testing."""
        
        variations = []
        
        for i in range(num_variations):
            # Generate unique headlines and descriptions
            headlines = self.generate_headlines(business_info, variation=i)
            descriptions = self.generate_descriptions(business_info, variation=i)
            
            # Create ad variation
            ad_variation = self.create_responsive_search_ad(
                ad_group_id=ad_group_id,
                headlines=headlines,
                descriptions=descriptions,
                final_urls=[business_info['website']]
            )
            
            if ad_variation:
                variations.append(ad_variation)
                
                # Create extensions for this variation
                self.create_extension_set(ad_group_id, business_info, variation=i)
        
        return variations
    
    def generate_headlines(self, business_info, variation=0):
        """Generate headlines with different strategies."""
        
        base_headlines = [
            f"{business_info['brand_name']} - {business_info['main_service']}",
            f"Professional {business_info['main_service']} Service",
            f"{business_info['unique_selling_point']}",
            f"Call {business_info['phone']} Today"
        ]
        
        if variation == 0:  # Benefit-focused
            variant_headlines = [
                f"Save Money on {business_info['main_service']}",
                f"Fast {business_info['main_service']} Solutions",
                f"Quality {business_info['main_service']} Guaranteed",
                f"Free Estimates Available"
            ]
        elif variation == 1:  # Trust-focused
            variant_headlines = [
                f"Licensed {business_info['main_service']} Experts",
                f"{business_info['experience_years']}+ Years Experience",
                f"Insured & Bonded Service",
                f"Trusted by {business_info['customers_served']}+ Customers"
            ]
        else:  # Urgency-focused
            variant_headlines = [
                f"Same Day {business_info['main_service']}",
                f"Emergency {business_info['main_service']} Available",
                f"Book Now - Limited Slots",
                f"Call Today - Don't Wait"
            ]
        
        return base_headlines + variant_headlines
    
    def generate_descriptions(self, business_info, variation=0):
        """Generate descriptions with different approaches."""
        
        if variation == 0:  # Benefit-focused
            descriptions = [
                f"Get the best {business_info['main_service']} at competitive prices. "
                f"Our expert team delivers quality results with fast turnaround times. "
                f"Free estimates and consultation available.",
                
                f"Why pay more for {business_info['main_service']}? {business_info['brand_name']} "
                f"offers premium service at affordable rates. Licensed, insured, and locally owned.",
                
                f"Experience the difference with {business_info['brand_name']}. "
                f"Professional {business_info['main_service']} with guaranteed satisfaction. "
                f"Call today for your free quote."
            ]
        elif variation == 1:  # Trust-focused
            descriptions = [
                f"Trust {business_info['brand_name']} for all your {business_info['main_service']} needs. "
                f"With {business_info['experience_years']}+ years of experience, we're the local experts. "
                f"Licensed, insured, and fully bonded.",
                
                f"Choose the {business_info['main_service']} company that {business_info['location']} "
                f"residents trust. We've served {business_info['customers_served']}+ satisfied customers "
                f"with reliable, professional service.",
                
                f"Award-winning {business_info['main_service']} from {business_info['brand_name']}. "
                f"Our certified technicians provide quality workmanship with industry-leading warranties. "
                f"Contact us for expert service."
            ]
        else:  # Urgency-focused
            descriptions = [
                f"Need {business_info['main_service']} now? {business_info['brand_name']} offers "
                f"same-day service with emergency availability. Don't wait - call today for immediate help.",
                
                f"Time-sensitive {business_info['main_service']} problems require fast solutions. "
                f"Our rapid response team is ready to help. Available 24/7 for emergency service.",
                
                f"Don't let {business_info['main_service']} issues get worse. Call {business_info['brand_name']} "
                f"now for immediate assistance. Same-day appointments available."
            ]
        
        return descriptions
    
    @handle_ad_creation_errors
    def create_responsive_search_ad(self, ad_group_id, headlines, descriptions, final_urls):
        """Create responsive search ad with error handling."""
        
        return create_responsive_search_ad(
            self.client, self.customer_id, ad_group_id, headlines, descriptions, final_urls
        )
    
    def create_extension_set(self, ad_group_id, business_info, variation=0):
        """Create extension set for ad variation."""
        
        # Get campaign ID from ad group
        campaign_id = self.get_campaign_id_from_ad_group(ad_group_id)
        
        if not campaign_id:
            return None
        
        # Create extensions based on variation
        extensions = {}
        
        try:
            # Sitelinks
            extensions['sitelinks'] = create_strategic_sitelinks(
                self.client, self.customer_id, campaign_id, business_info
            )
            
            # Callouts
            extensions['callouts'] = create_compelling_callouts(
                self.client, self.customer_id, campaign_id, business_info
            )
            
            # Structured snippets
            extensions['structured_snippets'] = create_relevant_structured_snippets(
                self.client, self.customer_id, campaign_id, business_info
            )
            
            return extensions
            
        except Exception as ex:
            print(f"Error creating extension set: {ex}")
            return None
    
    def get_campaign_id_from_ad_group(self, ad_group_id):
        """Get campaign ID from ad group ID."""
        
        ga_service = self.client.get_service("GoogleAdsService")
        
        query = f"""
            SELECT campaign.id
            FROM ad_group 
            WHERE ad_group.id = {ad_group_id}
        """
        
        try:
            response = ga_service.search(customer_id=self.customer_id, query=query)
            
            for row in response:
                return row.campaign.id
            
            return None
            
        except GoogleAdsException as ex:
            print(f"Error getting campaign ID: {ex}")
            return None
    
    def monitor_ad_performance(self, ad_resource_names, days=7):
        """Monitor performance of created ad variations."""
        
        ga_service = self.client.get_service("GoogleAdsService")
        
        # Build resource name filter
        resource_filter = " OR ".join([f"ad_group_ad.resource_name = '{name}'" for name in ad_resource_names])
        
        query = f"""
            SELECT 
                ad_group_ad.resource_name,
                ad_group_ad.ad.id,
                metrics.impressions,
                metrics.clicks,
                metrics.ctr,
                metrics.cost_micros,
                metrics.conversions,
                metrics.conversion_rate
            FROM ad_group_ad 
            WHERE ({resource_filter})
                AND segments.date DURING LAST_{days}_DAYS
        """
        
        try:
            response = ga_service.search(customer_id=self.customer_id, query=query)
            
            performance_data = []
            
            for row in response:
                performance_data.append({
                    "resource_name": row.ad_group_ad.resource_name,
                    "ad_id": row.ad_group_ad.ad.id,
                    "impressions": row.metrics.impressions,
                    "clicks": row.metrics.clicks,
                    "ctr": row.metrics.ctr,
                    "cost": row.metrics.cost_micros / 1000000,
                    "conversions": row.metrics.conversions,
                    "conversion_rate": row.metrics.conversion_rate
                })
            
            return performance_data
            
        except GoogleAdsException as ex:
            print(f"Error monitoring ad performance: {ex}")
            return None

# Usage example
def main():
    """Example usage of the Ad Copy Laboratory."""
    
    # Initialize client
    client = initialize_client()
    if not client:
        return
    
    # Business information
    business_info = {
        "brand_name": "Premium Plumbing Co",
        "main_service": "Plumbing Services",
        "location": "Chicago",
        "phone": "(*************",
        "website": "https://premiumplumbing.com",
        "mobile_website": "https://m.premiumplumbing.com",
        "unique_selling_point": "24/7 Emergency Service",
        "experience_years": "15",
        "customers_served": "5000",
        "free_offer": "Estimates",
        "guarantee": "100% Satisfaction",
        "service_path": "plumbing",
        "location_path": "chicago"
    }
    
    # Create Ad Copy Laboratory
    lab = AdCopyLaboratory(client, customer_id="1234567890")
    
    # Create ad variations
    variations = lab.create_ad_variations(
        ad_group_id="123456789",
        business_info=business_info,
        num_variations=3
    )
    
    if variations:
        print(f"Created {len(variations)} ad variations successfully")
        
        # Monitor performance after a few days
        variation_resource_names = [var.resource_name for var in variations]
        performance_data = lab.monitor_ad_performance(variation_resource_names, days=7)
        
        if performance_data:
            print("Performance monitoring data:")
            for perf in performance_data:
                print(f"  Ad {perf['ad_id']}: {perf['impressions']} impressions, {perf['clicks']} clicks, {perf['ctr']:.2%} CTR")

if __name__ == "__main__":
    main()
```

## Summary

This comprehensive guide covers all aspects of Google Ads API ad creation and extensions:

### Key Features Covered:
1. **Ad Creation**: Responsive search ads, text ads, and advanced features
2. **Extensions**: Sitelinks, callouts, structured snippets, price, and promotion extensions
3. **Policy Compliance**: Approval tracking, violation handling, and exemption requests
4. **Creative Management**: Image assets, video assets, and asset libraries
5. **Performance Tracking**: Comprehensive metrics and optimization insights
6. **Best Practices**: Optimization strategies and professional implementation
7. **Error Handling**: Comprehensive error management and retry logic

### For AdsAI Platform Integration:
- **AI-Powered Ad Copy Laboratory**: Complete system for creating and testing ad variations
- **Automated Extension Creation**: Strategic extension generation based on business type
- **Performance Monitoring**: Real-time tracking and optimization recommendations
- **Policy Compliance Automation**: Automated handling of approvals and violations

This guide provides the foundation for building sophisticated ad creation and management features in the AdsAI platform, enabling efficient campaign management across all Google Ads campaign types with AI-powered optimization capabilities.