# Google Ads Manager Accounts (MCC) Complete API Guide

## Table of Contents
1. [Overview & Core Concepts](#overview--core-concepts)
2. [MCC Hierarchy Structure](#mcc-hierarchy-structure)
3. [Account Linking & Management](#account-linking--management)
4. [Multi-Client Operations](#multi-client-operations)
5. [Cross-Account Reporting](#cross-account-reporting)
6. [Python Code Examples](#python-code-examples)
7. [Best Practices](#best-practices)
8. [API Reference](#api-reference)

---

## Overview & Core Concepts

### What are Manager Accounts (MCC)?

Manager Accounts (MCC - My Client Center) are Google Ads accounts used for administrative purposes, not for serving ads. They act as **single points of access** for managing multiple Google Ads accounts, enabling consolidated billing and administrative features across multiple client accounts.

### Key Benefits
- **Centralized Management**: Control multiple client accounts from one dashboard
- **Consolidated Billing**: Unified billing across all managed accounts
- **Account Hierarchy**: Organize accounts in a structured hierarchy
- **Cross-Account Operations**: Perform bulk operations across multiple accounts
- **Shared Resources**: Use cross-account bidding strategies and conversion tracking

### MCC Account Limitations
- **Hierarchy Depth**: Maximum 6 levels deep
- **Manager Links**: Each account can have up to 5 manager accounts
- **Single Manager**: Each account can have only one direct manager
- **Access Rights**: Manager accounts inherit permissions from their hierarchy position

---

## MCC Hierarchy Structure

### Hierarchy Rules

```
Top-Level Manager Account (MCC)
├── Client Account 1
├── Client Account 2
├── Sub-Manager Account 1
│   ├── Client Account 3
│   ├── Client Account 4
│   └── Sub-Manager Account 2
│       ├── Client Account 5
│       └── Client Account 6
└── Client Account 7
```

### Hierarchy Constraints
- **Maximum Depth**: 6 levels maximum
- **Single Parent**: Each account can have only one direct manager
- **Multiple Managers**: Individual accounts can be linked to up to 5 manager accounts
- **Inheritance**: Permissions flow down the hierarchy

### API Services for Hierarchy Management

#### CustomerService
- `ListAccessibleCustomers()`: List accounts accessible to your credentials
- `CreateCustomerClient()`: Create new client accounts under a manager

#### GoogleAdsService
- `Search()`: Query account hierarchy using GAQL
- `SearchStream()`: Stream large hierarchy data

---

## Account Linking & Management

### Account Linking Process

Account linking is a **three-step process** that must be initiated from the manager account:

#### Step 1: Manager Extends Invitation
```python
# Manager account extends PENDING invitation to client
client_link = client.get_type("CustomerClientLink")
client_link.client_customer = f"customers/{client_customer_id}"
client_link.status = client.enums.ManagerLinkStatusEnum.PENDING

customer_client_link_service = client.get_service("CustomerClientLinkService")
operation = client.get_type("CustomerClientLinkOperation")
operation.create = client_link

response = customer_client_link_service.mutate_customer_client_link(
    customer_id=manager_customer_id,
    operation=operation
)
```

#### Step 2: Retrieve Manager Link ID
```python
# Query to find the manager_link_id
googleads_service = client.get_service("GoogleAdsService")
query = f"""
    SELECT
        customer_client_link.manager_link_id,
        customer_client_link.client_customer,
        customer_client_link.status
    FROM customer_client_link
    WHERE customer_client_link.client_customer = 'customers/{client_customer_id}'
"""

response = googleads_service.search(
    customer_id=manager_customer_id,
    query=query
)

manager_link_id = response.results[0].customer_client_link.manager_link_id
```

#### Step 3: Client Accepts Invitation
```python
# Client account accepts the invitation
manager_link = client.get_type("CustomerManagerLink")
manager_link.resource_name = f"customers/{client_customer_id}/customerManagerLinks/{manager_link_id}"
manager_link.status = client.enums.ManagerLinkStatusEnum.ACTIVE

customer_manager_link_service = client.get_service("CustomerManagerLinkService")
operation = client.get_type("CustomerManagerLinkOperation")
operation.update = manager_link
operation.update_mask = client.get_type("FieldMask")
operation.update_mask.paths.append("status")

response = customer_manager_link_service.mutate_customer_manager_link(
    customer_id=client_customer_id,
    operations=[operation]
)
```

### Service Perspectives

#### CustomerClientLinkService (Manager's View)
- Used when manager looks "down" at client accounts
- Manages outbound links from manager to clients
- Creates and monitors invitation status

#### CustomerManagerLinkService (Client's View)
- Used when client looks "up" at manager accounts
- Manages inbound links from managers
- Accepts or rejects manager invitations

---

## Multi-Client Operations

### Login Customer ID for Cross-Account Operations

The `login-customer-id` header is **crucial** for manager account operations:

```python
# Set login-customer-id for manager account operations
client = GoogleAdsClient.load_from_storage(
    path="path/to/google-ads.yaml",
    version="v18"
)

# For cross-account operations, set the manager account as login customer
client.login_customer_id = "**********"  # Manager account ID without hyphens
```

### Key Concepts

#### Operating Customer vs Login Customer
- **Operating Customer**: The customer ID in the request payload (target account)
- **Login Customer**: The customer ID you're authenticated as (manager account)

#### When to Use login-customer-id
- **Required**: When accessing client accounts through a manager account
- **Cross-Account Operations**: Bidding strategies, conversion tracking, reporting
- **Account Creation**: Creating new client accounts under a manager

### Creating Client Accounts

```python
def create_client_account(client, manager_customer_id):
    """Creates a new client account under a manager account."""
    
    customer_service = client.get_service("CustomerService")
    
    # Create customer object
    customer = client.get_type("Customer")
    customer.descriptive_name = "New Client Account"
    customer.currency_code = "USD"
    customer.time_zone = "America/New_York"
    
    # Create the client account
    response = customer_service.create_customer_client(
        customer_id=manager_customer_id,
        customer_client=customer
    )
    
    return response.resource_name
```

### Listing Accessible Accounts

```python
def list_accessible_customers(client):
    """Lists all accessible customer accounts."""
    
    customer_service = client.get_service("CustomerService")
    
    # This method doesn't require customer_id and ignores login-customer-id
    accessible_customers = customer_service.list_accessible_customers()
    
    customer_ids = []
    for resource_name in accessible_customers.resource_names:
        # Parse customer ID from resource name
        customer_id = client.get_service("GoogleAdsService").parse_customer_path(
            resource_name
        )["customer_id"]
        customer_ids.append(customer_id)
    
    return customer_ids
```

---

## Cross-Account Reporting

### Cross-Account Bidding Strategies

Cross-account bidding strategies are Smart Bidding portfolio strategies created in manager accounts that can be used by campaigns in client accounts.

#### Creating Cross-Account Bidding Strategy

```python
def create_cross_account_bidding_strategy(client, manager_customer_id):
    """Creates a cross-account bidding strategy in a manager account."""
    
    bidding_strategy_service = client.get_service("BiddingStrategyService")
    
    # Create bidding strategy
    bidding_strategy = client.get_type("BiddingStrategy")
    bidding_strategy.name = "Cross-Account Target CPA Strategy"
    bidding_strategy.target_cpa.target_cpa_micros = 5000000  # $5.00
    bidding_strategy.currency_code = "USD"
    
    operation = client.get_type("BiddingStrategyOperation")
    operation.create = bidding_strategy
    
    response = bidding_strategy_service.mutate_bidding_strategies(
        customer_id=manager_customer_id,
        operations=[operation]
    )
    
    return response.results[0].resource_name
```

#### Attaching Cross-Account Strategy to Campaign

```python
def attach_cross_account_strategy(client, client_customer_id, campaign_id, strategy_resource_name):
    """Attaches a cross-account bidding strategy to a campaign."""
    
    # Must use manager account as login-customer-id
    client.login_customer_id = "**********"  # Manager account ID
    
    campaign_service = client.get_service("CampaignService")
    
    campaign = client.get_type("Campaign")
    campaign.resource_name = f"customers/{client_customer_id}/campaigns/{campaign_id}"
    campaign.bidding_strategy = strategy_resource_name
    
    operation = client.get_type("CampaignOperation")
    operation.update = campaign
    operation.update_mask = client.get_type("FieldMask")
    operation.update_mask.paths.append("bidding_strategy")
    
    response = campaign_service.mutate_campaigns(
        customer_id=client_customer_id,
        operations=[operation]
    )
    
    return response.results[0].resource_name
```

### Cross-Account Conversion Tracking

```python
def setup_cross_account_conversion_tracking(client, manager_customer_id):
    """Sets up cross-account conversion tracking."""
    
    conversion_action_service = client.get_service("ConversionActionService")
    
    # Create conversion action in manager account
    conversion_action = client.get_type("ConversionAction")
    conversion_action.name = "Cross-Account Purchase"
    conversion_action.type_ = client.enums.ConversionActionTypeEnum.PURCHASE
    conversion_action.category = client.enums.ConversionActionCategoryEnum.PURCHASE
    conversion_action.status = client.enums.ConversionActionStatusEnum.ENABLED
    conversion_action.view_through_lookback_window_days = 30
    
    operation = client.get_type("ConversionActionOperation")
    operation.create = conversion_action
    
    response = conversion_action_service.mutate_conversion_actions(
        customer_id=manager_customer_id,
        operations=[operation]
    )
    
    return response.results[0].resource_name
```

---

## Python Code Examples

### Complete Account Hierarchy Retrieval

```python
def get_account_hierarchy(client, login_customer_id=None):
    """Gets the complete account hierarchy for MCC accounts."""
    
    googleads_service = client.get_service("GoogleAdsService")
    customer_service = client.get_service("CustomerService")
    
    # GAQL query to retrieve account hierarchy
    query = """
        SELECT
            customer_client.client_customer,
            customer_client.level,
            customer_client.manager,
            customer_client.descriptive_name,
            customer_client.currency_code,
            customer_client.time_zone,
            customer_client.id
        FROM customer_client
        WHERE customer_client.level <= 1
    """
    
    seed_customer_ids = []
    
    if login_customer_id:
        seed_customer_ids = [login_customer_id]
    else:
        # Get all accessible customers
        accessible_customers = customer_service.list_accessible_customers()
        for resource_name in accessible_customers.resource_names:
            customer_id = googleads_service.parse_customer_path(resource_name)["customer_id"]
            seed_customer_ids.append(customer_id)
    
    # Process each seed customer
    for seed_customer_id in seed_customer_ids:
        print(f"\nHierarchy for customer {seed_customer_id}:")
        
        unprocessed_customer_ids = [seed_customer_id]
        customer_ids_to_child_accounts = {}
        root_customer_client = None
        
        # Process hierarchy level by level
        while unprocessed_customer_ids:
            customer_id = unprocessed_customer_ids.pop(0)
            
            response = googleads_service.search(
                customer_id=str(customer_id),
                query=query
            )
            
            # Process each customer in the response
            for row in response:
                customer_client = row.customer_client
                
                # Handle root customer
                if customer_client.level == 0:
                    if not root_customer_client:
                        root_customer_client = customer_client
                    continue
                
                # Handle child accounts
                if customer_client.manager:
                    # This is a manager account
                    if customer_client.level == 1:
                        unprocessed_customer_ids.append(customer_client.id)
                else:
                    # This is a client account
                    pass
                
                # Build parent-child mapping
                parent_id = customer_id
                if parent_id not in customer_ids_to_child_accounts:
                    customer_ids_to_child_accounts[parent_id] = []
                customer_ids_to_child_accounts[parent_id].append(customer_client)
        
        # Print hierarchy
        if root_customer_client:
            print_hierarchy(root_customer_client, customer_ids_to_child_accounts, 0)

def print_hierarchy(customer_client, customer_ids_to_child_accounts, depth):
    """Prints the account hierarchy in a tree format."""
    
    indent = "  " * depth
    account_type = "Manager" if customer_client.manager else "Client"
    
    print(f"{indent}{customer_client.descriptive_name} ({customer_client.id}) - {account_type}")
    print(f"{indent}  Currency: {customer_client.currency_code}")
    print(f"{indent}  Time Zone: {customer_client.time_zone}")
    
    # Print child accounts
    if customer_client.id in customer_ids_to_child_accounts:
        for child_customer in customer_ids_to_child_accounts[customer_client.id]:
            print_hierarchy(child_customer, customer_ids_to_child_accounts, depth + 1)
```

### Bulk Account Operations

```python
def bulk_account_operations(client, manager_customer_id, client_customer_ids):
    """Performs bulk operations across multiple client accounts."""
    
    # Set manager account as login customer
    client.login_customer_id = manager_customer_id
    
    results = {}
    
    for client_id in client_customer_ids:
        try:
            # Get account performance data
            performance_data = get_account_performance(client, client_id)
            results[client_id] = performance_data
            
            # Check if account needs attention
            if performance_data.get('cost_per_click') > 2.0:  # $2.00 threshold
                print(f"Account {client_id} needs attention - High CPC: ${performance_data['cost_per_click']}")
                
                # Automatically adjust bids
                adjust_account_bids(client, client_id, adjustment_factor=0.9)
                
        except Exception as e:
            print(f"Error processing account {client_id}: {e}")
            results[client_id] = {"error": str(e)}
    
    return results

def get_account_performance(client, customer_id):
    """Gets performance metrics for a specific account."""
    
    googleads_service = client.get_service("GoogleAdsService")
    
    query = """
        SELECT
            metrics.cost_micros,
            metrics.clicks,
            metrics.impressions,
            metrics.average_cpc
        FROM customer
        WHERE segments.date DURING LAST_7_DAYS
    """
    
    response = googleads_service.search(
        customer_id=customer_id,
        query=query
    )
    
    total_cost = 0
    total_clicks = 0
    total_impressions = 0
    
    for row in response:
        total_cost += row.metrics.cost_micros
        total_clicks += row.metrics.clicks
        total_impressions += row.metrics.impressions
    
    return {
        "cost": total_cost / 1_000_000,  # Convert micros to currency
        "clicks": total_clicks,
        "impressions": total_impressions,
        "cost_per_click": (total_cost / total_clicks / 1_000_000) if total_clicks > 0 else 0
    }

def adjust_account_bids(client, customer_id, adjustment_factor=0.9):
    """Adjusts bids for all campaigns in an account."""
    
    campaign_service = client.get_service("CampaignService")
    googleads_service = client.get_service("GoogleAdsService")
    
    # Get all campaigns with manual CPC bidding
    query = """
        SELECT
            campaign.id,
            campaign.name,
            campaign.manual_cpc.enhanced_cpc_enabled
        FROM campaign
        WHERE campaign.status = 'ENABLED'
        AND campaign.bidding_strategy_type = 'MANUAL_CPC'
    """
    
    response = googleads_service.search(
        customer_id=customer_id,
        query=query
    )
    
    operations = []
    
    for row in response:
        campaign_id = row.campaign.id
        
        # Get keyword bids for this campaign
        keyword_bids = get_keyword_bids(client, customer_id, campaign_id)
        
        # Adjust keyword bids
        for keyword_resource_name, current_bid in keyword_bids.items():
            new_bid = int(current_bid * adjustment_factor)
            
            # Create bid adjustment operation
            operation = create_bid_adjustment_operation(
                client, keyword_resource_name, new_bid
            )
            operations.append(operation)
    
    # Execute bulk bid adjustments
    if operations:
        ad_group_criterion_service = client.get_service("AdGroupCriterionService")
        response = ad_group_criterion_service.mutate_ad_group_criteria(
            customer_id=customer_id,
            operations=operations
        )
        
        print(f"Adjusted {len(response.results)} keyword bids for account {customer_id}")

def get_keyword_bids(client, customer_id, campaign_id):
    """Gets current keyword bids for a campaign."""
    
    googleads_service = client.get_service("GoogleAdsService")
    
    query = f"""
        SELECT
            ad_group_criterion.resource_name,
            ad_group_criterion.cpc_bid_micros,
            ad_group_criterion.keyword.text
        FROM ad_group_criterion
        WHERE campaign.id = {campaign_id}
        AND ad_group_criterion.type = 'KEYWORD'
        AND ad_group_criterion.status = 'ENABLED'
    """
    
    response = googleads_service.search(
        customer_id=customer_id,
        query=query
    )
    
    keyword_bids = {}
    for row in response:
        keyword_bids[row.ad_group_criterion.resource_name] = row.ad_group_criterion.cpc_bid_micros
    
    return keyword_bids

def create_bid_adjustment_operation(client, keyword_resource_name, new_bid_micros):
    """Creates a bid adjustment operation."""
    
    ad_group_criterion = client.get_type("AdGroupCriterion")
    ad_group_criterion.resource_name = keyword_resource_name
    ad_group_criterion.cpc_bid_micros = new_bid_micros
    
    operation = client.get_type("AdGroupCriterionOperation")
    operation.update = ad_group_criterion
    operation.update_mask = client.get_type("FieldMask")
    operation.update_mask.paths.append("cpc_bid_micros")
    
    return operation
```

### Multi-Client Reporting

```python
def generate_multi_client_report(client, manager_customer_id, client_customer_ids):
    """Generates a comprehensive report across multiple client accounts."""
    
    # Set manager account as login customer
    client.login_customer_id = manager_customer_id
    
    googleads_service = client.get_service("GoogleAdsService")
    
    # Query for multi-client performance data
    query = """
        SELECT
            customer.descriptive_name,
            customer.id,
            customer.currency_code,
            metrics.cost_micros,
            metrics.clicks,
            metrics.impressions,
            metrics.conversions,
            metrics.conversion_value_micros,
            metrics.average_cpc,
            metrics.ctr,
            metrics.cost_per_conversion
        FROM customer
        WHERE segments.date DURING LAST_30_DAYS
    """
    
    report_data = []
    
    for client_id in client_customer_ids:
        try:
            response = googleads_service.search(
                customer_id=client_id,
                query=query
            )
            
            for row in response:
                customer_data = {
                    "account_id": row.customer.id,
                    "account_name": row.customer.descriptive_name,
                    "currency": row.customer.currency_code,
                    "cost": row.metrics.cost_micros / 1_000_000,
                    "clicks": row.metrics.clicks,
                    "impressions": row.metrics.impressions,
                    "conversions": row.metrics.conversions,
                    "conversion_value": row.metrics.conversion_value_micros / 1_000_000,
                    "average_cpc": row.metrics.average_cpc / 1_000_000,
                    "ctr": row.metrics.ctr,
                    "cost_per_conversion": row.metrics.cost_per_conversion / 1_000_000
                }
                report_data.append(customer_data)
                
        except Exception as e:
            print(f"Error getting data for account {client_id}: {e}")
    
    # Generate summary report
    summary = generate_report_summary(report_data)
    
    return {
        "detailed_data": report_data,
        "summary": summary
    }

def generate_report_summary(report_data):
    """Generates summary statistics for the multi-client report."""
    
    if not report_data:
        return {}
    
    total_cost = sum(row["cost"] for row in report_data)
    total_clicks = sum(row["clicks"] for row in report_data)
    total_impressions = sum(row["impressions"] for row in report_data)
    total_conversions = sum(row["conversions"] for row in report_data)
    total_conversion_value = sum(row["conversion_value"] for row in report_data)
    
    return {
        "total_accounts": len(report_data),
        "total_cost": total_cost,
        "total_clicks": total_clicks,
        "total_impressions": total_impressions,
        "total_conversions": total_conversions,
        "total_conversion_value": total_conversion_value,
        "average_cpc": total_cost / total_clicks if total_clicks > 0 else 0,
        "overall_ctr": total_clicks / total_impressions if total_impressions > 0 else 0,
        "overall_conversion_rate": total_conversions / total_clicks if total_clicks > 0 else 0,
        "overall_roas": total_conversion_value / total_cost if total_cost > 0 else 0
    }
```

---

## Best Practices

### 1. Account Hierarchy Design

#### Recommended Structure
```
Brand Wisdom MCC (Top Level)
├── E-commerce Clients (Sub-Manager)
│   ├── Fashion Store 1
│   ├── Fashion Store 2
│   └── Electronics Store
├── B2B Clients (Sub-Manager)
│   ├── SaaS Company 1
│   ├── SaaS Company 2
│   └── Consulting Firm
└── Local Businesses (Sub-Manager)
    ├── Restaurant 1
    ├── Restaurant 2
    └── Dental Practice
```

#### Design Principles
- **Logical Grouping**: Group similar business types together
- **Scalability**: Design for future growth
- **Access Control**: Use sub-managers for team access control
- **Billing Structure**: Align with billing and reporting needs

### 2. Authentication & Access Management

#### login-customer-id Best Practices
```python
# Always set login-customer-id for cross-account operations
client.login_customer_id = manager_account_id

# Use the highest-level manager account that has access
# to all required resources
```

#### Account Access Patterns
- **Principle of Least Privilege**: Grant minimum required access
- **Regular Audits**: Review account access regularly
- **Automated Monitoring**: Monitor for unauthorized access attempts

### 3. Bulk Operations

#### Batch Processing
```python
# Process accounts in batches to avoid rate limits
def process_accounts_in_batches(client, account_ids, batch_size=10):
    for i in range(0, len(account_ids), batch_size):
        batch = account_ids[i:i + batch_size]
        process_account_batch(client, batch)
        time.sleep(1)  # Rate limiting
```

#### Error Handling
```python
def robust_bulk_operation(client, account_ids, operation_func):
    results = {}
    for account_id in account_ids:
        try:
            result = operation_func(client, account_id)
            results[account_id] = {"success": True, "data": result}
        except Exception as e:
            results[account_id] = {"success": False, "error": str(e)}
            # Log error but continue processing
            logging.error(f"Error processing {account_id}: {e}")
    return results
```

### 4. Performance Optimization

#### Caching Strategy
```python
# Cache account hierarchy to avoid repeated API calls
@lru_cache(maxsize=100)
def get_cached_account_hierarchy(manager_id):
    return get_account_hierarchy(client, manager_id)
```

#### Parallel Processing
```python
from concurrent.futures import ThreadPoolExecutor

def parallel_account_processing(client, account_ids, max_workers=5):
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(process_single_account, client, account_id): account_id
            for account_id in account_ids
        }
        
        results = {}
        for future in concurrent.futures.as_completed(futures):
            account_id = futures[future]
            try:
                result = future.result()
                results[account_id] = result
            except Exception as e:
                results[account_id] = {"error": str(e)}
        
        return results
```

### 5. Monitoring & Alerting

#### Account Health Monitoring
```python
def monitor_account_health(client, account_ids):
    """Monitors account health and sends alerts."""
    
    alerts = []
    
    for account_id in account_ids:
        health_metrics = get_account_health_metrics(client, account_id)
        
        # Check for budget exhaustion
        if health_metrics.get("budget_utilization", 0) > 0.95:
            alerts.append({
                "account_id": account_id,
                "type": "budget_exhaustion",
                "message": f"Account {account_id} has used 95%+ of budget"
            })
        
        # Check for performance drops
        if health_metrics.get("ctr_change", 0) < -0.2:  # 20% CTR drop
            alerts.append({
                "account_id": account_id,
                "type": "performance_drop",
                "message": f"Account {account_id} CTR dropped by 20%+"
            })
    
    # Send alerts
    if alerts:
        send_alerts(alerts)
    
    return alerts
```

---

## API Reference

### Core Services

#### CustomerService
```python
# Methods for account management
customer_service = client.get_service("CustomerService")

# List accessible customers
accessible_customers = customer_service.list_accessible_customers()

# Create new client account
new_account = customer_service.create_customer_client(
    customer_id=manager_id,
    customer_client=customer_object
)

# Get customer details
customer = customer_service.get_customer(
    resource_name=f"customers/{customer_id}"
)
```

#### CustomerClientLinkService
```python
# Manager's view of client links
client_link_service = client.get_service("CustomerClientLinkService")

# Create client link (manager side)
response = client_link_service.mutate_customer_client_link(
    customer_id=manager_id,
    operation=create_operation
)

# Get client links
links = client_link_service.get_customer_client_link(
    resource_name=link_resource_name
)
```

#### CustomerManagerLinkService
```python
# Client's view of manager links
manager_link_service = client.get_service("CustomerManagerLinkService")

# Accept manager invitation (client side)
response = manager_link_service.mutate_customer_manager_link(
    customer_id=client_id,
    operations=[accept_operation]
)

# Get manager links
links = manager_link_service.get_customer_manager_link(
    resource_name=link_resource_name
)
```

### GAQL Queries for MCC Operations

#### Account Hierarchy Query
```sql
SELECT
    customer_client.client_customer,
    customer_client.level,
    customer_client.manager,
    customer_client.descriptive_name,
    customer_client.currency_code,
    customer_client.time_zone,
    customer_client.id
FROM customer_client
WHERE customer_client.level <= 1
```

#### Client Links Query
```sql
SELECT
    customer_client_link.client_customer,
    customer_client_link.manager_link_id,
    customer_client_link.status,
    customer_client_link.hidden
FROM customer_client_link
WHERE customer_client_link.status = 'ACTIVE'
```

#### Manager Links Query
```sql
SELECT
    customer_manager_link.manager_customer,
    customer_manager_link.manager_link_id,
    customer_manager_link.status
FROM customer_manager_link
WHERE customer_manager_link.status = 'ACTIVE'
```

#### Cross-Account Bidding Strategies Query
```sql
SELECT
    accessible_bidding_strategy.id,
    accessible_bidding_strategy.name,
    accessible_bidding_strategy.type,
    accessible_bidding_strategy.owner_customer_id,
    accessible_bidding_strategy.owner_descriptive_name
FROM accessible_bidding_strategy
WHERE accessible_bidding_strategy.owner_customer_id = '**********'
```

### Resource Names

#### Customer Resource Names
```python
# Customer resource name format
customer_resource_name = f"customers/{customer_id}"

# Parse customer ID from resource name
customer_id = googleads_service.parse_customer_path(
    customer_resource_name
)["customer_id"]
```

#### Link Resource Names
```python
# Customer client link resource name
client_link_resource_name = f"customers/{manager_id}/customerClientLinks/{link_id}"

# Customer manager link resource name
manager_link_resource_name = f"customers/{client_id}/customerManagerLinks/{link_id}"
```

### Rate Limits & Quotas

#### API Rate Limits
- **Daily Operations**: 15,000 operations per day
- **Concurrent Requests**: 100 requests per 100 seconds
- **Report Downloads**: 2,000 reports per day

#### Best Practices for Rate Limiting
```python
import time
from google.ads.googleads.errors import GoogleAdsException

def rate_limited_operation(client, operation_func, *args, **kwargs):
    """Executes operation with rate limiting."""
    max_retries = 3
    retry_delay = 1
    
    for attempt in range(max_retries):
        try:
            return operation_func(*args, **kwargs)
        except GoogleAdsException as e:
            if "RATE_EXCEEDED" in str(e):
                if attempt < max_retries - 1:
                    time.sleep(retry_delay * (2 ** attempt))  # Exponential backoff
                    continue
            raise
    
    raise Exception("Max retries exceeded")
```

---

## Troubleshooting

### Common Issues & Solutions

#### 1. Authentication Errors
```python
# Error: "Request is missing required authentication credential"
# Solution: Ensure login-customer-id is set correctly

client.login_customer_id = "**********"  # Manager account ID
```

#### 2. Access Denied Errors
```python
# Error: "User doesn't have permission to access customer"
# Solution: Check account linking and permissions

# Verify account link exists
query = """
    SELECT customer_manager_link.manager_customer,
           customer_manager_link.status
    FROM customer_manager_link
    WHERE customer_manager_link.manager_customer = 'customers/**********'
"""
```

#### 3. Invalid Resource Names
```python
# Error: "Invalid resource name"
# Solution: Ensure proper resource name format

# Correct format
resource_name = f"customers/{customer_id}/campaigns/{campaign_id}"

# Parse and validate
parsed = googleads_service.parse_campaign_path(resource_name)
```

### Debug Tools

#### Enable Debug Logging
```python
import logging

# Enable debug logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("google.ads.googleads")
logger.setLevel(logging.DEBUG)
```

#### Request/Response Inspection
```python
def debug_request(client, request_func, *args, **kwargs):
    """Wraps requests with debug information."""
    try:
        response = request_func(*args, **kwargs)
        print(f"Request successful: {response}")
        return response
    except Exception as e:
        print(f"Request failed: {e}")
        print(f"Request args: {args}")
        print(f"Request kwargs: {kwargs}")
        raise
```

---

## Conclusion

This comprehensive guide covers all aspects of Google Ads Manager Accounts (MCC) API operations, from basic account hierarchy management to advanced cross-account operations. The provided Python code examples demonstrate real-world implementation patterns for managing multi-client Google Ads accounts programmatically.

### Key Takeaways

1. **MCC Structure**: Design your account hierarchy thoughtfully for scalability and access control
2. **Authentication**: Always use `login-customer-id` for cross-account operations
3. **Bulk Operations**: Implement proper error handling and rate limiting
4. **Cross-Account Features**: Leverage shared resources like bidding strategies and conversion tracking
5. **Monitoring**: Set up comprehensive monitoring and alerting for account health

### Next Steps

1. **Implementation**: Start with basic account hierarchy retrieval
2. **Testing**: Use Google Ads API test accounts for development
3. **Production**: Implement proper error handling and monitoring
4. **Optimization**: Use caching and parallel processing for performance
5. **Monitoring**: Set up alerts for account health and performance

This guide provides the foundation for building robust multi-client Google Ads management platforms using the Google Ads API and Manager Accounts (MCC) functionality.