# Complete Google Ads API Error Handling Guide

## Table of Contents
1. [<PERSON>rro<PERSON> Handling Overview](#error-handling-overview)
2. [Error Types and Categories](#error-types-and-categories)
3. [Python Error Handling Patterns](#python-error-handling-patterns)
4. [Rate Limiting and Quota Management](#rate-limiting-and-quota-management)
5. [Common Errors and Solutions](#common-errors-and-solutions)
6. [Performance Optimization](#performance-optimization)
7. [Production Deployment Best Practices](#production-deployment-best-practices)
8. [Debugging and Troubleshooting](#debugging-and-troubleshooting)
9. [Complete Code Examples](#complete-code-examples)

---

## Error Handling Overview

The Google Ads API uses structured error reporting through JSON failure objects containing error codes and detailed messages. All client libraries throw exceptions that encapsulate these errors, providing comprehensive information for debugging and error handling.

### Core Error Handling Principles

1. **Categorize Errors**: Different error types require different handling strategies
2. **Provide Context**: Use specific error information to give users actionable feedback
3. **Implement Retry Logic**: Handle transient errors with exponential backoff
4. **Log Comprehensively**: Include request IDs and error details for debugging
5. **Monitor Trends**: Track error patterns to identify systemic issues

---

## Error Types and Categories

### 1. Authentication Errors
**Cause**: Invalid credentials, expired tokens, or insufficient permissions
**Handling**: Re-initiate OAuth2 flow or refresh tokens

```python
from google.ads.googleads.errors import GoogleAdsException

def handle_authentication_error(error):
    """Handle authentication-related errors"""
    if error.error.error_code == 'AUTHENTICATION_ERROR':
        # Redirect to OAuth2 flow
        return redirect_to_oauth()
    elif error.error.error_code == 'AUTHORIZATION_ERROR':
        # Request additional permissions
        return request_additional_scopes()
```

### 2. Retryable Errors
**Cause**: Temporary server issues, network problems, or rate limiting
**Handling**: Implement exponential backoff retry strategy

```python
import time
import random
from google.ads.googleads.errors import GoogleAdsException

def exponential_backoff_retry(func, max_retries=5, base_delay=1):
    """Retry function with exponential backoff"""
    for attempt in range(max_retries):
        try:
            return func()
        except GoogleAdsException as ex:
            if is_retryable_error(ex) and attempt < max_retries - 1:
                delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                time.sleep(delay)
                continue
            raise
    
def is_retryable_error(exception):
    """Check if error is retryable"""
    retryable_errors = [
        'TRANSIENT_ERROR',
        'INTERNAL_ERROR',
        'RESOURCE_TEMPORARILY_EXHAUSTED',
        'DEADLINE_EXCEEDED'
    ]
    return any(error.error_code in retryable_errors 
               for error in exception.failure.errors)
```

### 3. Validation Errors
**Cause**: Invalid input data, constraint violations, or business rule violations
**Handling**: Validate input and provide specific error messages to users

```python
def handle_validation_error(error):
    """Handle validation errors with specific user feedback"""
    error_details = []
    
    for err in error.failure.errors:
        field_path = ' -> '.join([element.field_name 
                                 for element in err.location.field_path_elements])
        error_details.append({
            'field': field_path,
            'code': err.error_code,
            'message': err.message,
            'trigger': err.trigger
        })
    
    return {
        'status': 'validation_error',
        'errors': error_details,
        'request_id': error.request_id
    }
```

### 4. Policy Violations
**Cause**: Content that violates Google Ads policies
**Handling**: Flag for human review or suggest policy-compliant alternatives

```python
def handle_policy_violation(error):
    """Handle policy violation errors"""
    violations = []
    
    for err in error.failure.errors:
        if err.error_code == 'POLICY_VIOLATION_ERROR':
            violations.append({
                'policy_name': err.details.policy_name,
                'violation_type': err.details.violation_type,
                'key': err.details.key,
                'evidence': err.details.evidence
            })
    
    return {
        'status': 'policy_violation',
        'violations': violations,
        'requires_human_review': True
    }
```

---

## Python Error Handling Patterns

### Complete Error Handler Function

```python
from google.ads.googleads.errors import GoogleAdsException
import logging

def handle_googleads_exception(ex):
    """
    Comprehensive error handler for Google Ads API exceptions
    """
    logger = logging.getLogger(__name__)
    
    # Log request ID for debugging
    logger.error(f"Request ID: {ex.request_id}")
    logger.error(f"Request failed with status: {ex.failure.errors[0].error_code}")
    
    error_details = []
    
    # Process each error in the failure
    for error in ex.failure.errors:
        error_info = {
            'error_code': error.error_code,
            'message': error.message,
            'trigger': error.trigger,
            'location': []
        }
        
        # Extract field path information
        if error.location:
            for field_path_element in error.location.field_path_elements:
                error_info['location'].append({
                    'field_name': field_path_element.field_name,
                    'index': field_path_element.index
                })
        
        error_details.append(error_info)
        
        # Log detailed error information
        logger.error(f"Error: {error.error_code}")
        logger.error(f"Message: {error.message}")
        
        if error.location:
            for field_path_element in error.location.field_path_elements:
                logger.error(f"On field: {field_path_element.field_name}")
    
    return {
        'request_id': ex.request_id,
        'status': 'error',
        'errors': error_details
    }
```

### Service-Specific Error Handling

```python
def safe_api_call(service_method, *args, **kwargs):
    """
    Wrapper for safe API calls with comprehensive error handling
    """
    try:
        return service_method(*args, **kwargs)
    
    except GoogleAdsException as ex:
        # Handle specific error types
        for error in ex.failure.errors:
            if error.error_code == 'CUSTOMER_NOT_ENABLED':
                return {'error': 'Customer account is not enabled for Google Ads API'}
            elif error.error_code == 'CUSTOMER_NOT_FOUND':
                return {'error': 'Customer ID not found or not accessible'}
            elif error.error_code == 'AUTHORIZATION_ERROR':
                return {'error': 'Insufficient permissions for this operation'}
            elif error.error_code == 'QUOTA_ERROR':
                return {'error': 'API quota exceeded. Please try again later.'}
        
        # Generic error handling
        return handle_googleads_exception(ex)
    
    except Exception as ex:
        # Handle unexpected errors
        logging.error(f"Unexpected error: {ex}")
        return {'error': 'An unexpected error occurred'}
```

---

## Rate Limiting and Quota Management

### Understanding API Limits

The Google Ads API implements several types of limits:

1. **Daily Operations**: 15,000 operations per day for basic access
2. **Operations per Request**: Maximum 10,000 operations per mutate request
3. **Message Size**: 64 MB maximum per request
4. **Concurrent Requests**: Dynamic rate limiting based on QPS per customer ID

### Rate Limiting Implementation

```python
import time
from math import ceil
from threading import Lock
from collections import defaultdict

class RateLimiter:
    """
    Token bucket rate limiter for Google Ads API
    """
    
    def __init__(self, requests_per_second=1.0, burst_size=5):
        self.requests_per_second = requests_per_second
        self.burst_size = burst_size
        self.tokens = burst_size
        self.last_update = time.time()
        self.lock = Lock()
    
    def acquire(self):
        """Acquire a token for API request"""
        with self.lock:
            now = time.time()
            # Add tokens based on elapsed time
            elapsed = now - self.last_update
            self.tokens = min(self.burst_size, 
                             self.tokens + elapsed * self.requests_per_second)
            self.last_update = now
            
            if self.tokens >= 1:
                self.tokens -= 1
                return True
            else:
                # Calculate wait time
                wait_time = (1 - self.tokens) / self.requests_per_second
                return wait_time

class GoogleAdsRateLimiter:
    """
    Rate limiter specifically for Google Ads API
    """
    
    def __init__(self):
        self.limiters = defaultdict(lambda: RateLimiter(requests_per_second=0.167))  # 1 request per 6 seconds
    
    def wait_if_needed(self, customer_id):
        """Wait if rate limit would be exceeded"""
        limiter = self.limiters[customer_id]
        result = limiter.acquire()
        
        if result is not True:
            wait_time = result
            time.sleep(wait_time)

# Usage example
rate_limiter = GoogleAdsRateLimiter()

def make_api_request(customer_id, request_func, *args, **kwargs):
    """Make API request with rate limiting"""
    rate_limiter.wait_if_needed(customer_id)
    return request_func(*args, **kwargs)
```

### Advanced Rate Limiting with Exponential Backoff

```python
import time
import random
from google.ads.googleads.errors import GoogleAdsException

def api_request_with_backoff(request_func, max_retries=5, base_delay=2):
    """
    Make API request with exponential backoff for rate limiting
    """
    for attempt in range(max_retries):
        try:
            return request_func()
        
        except GoogleAdsException as ex:
            # Check if it's a rate limit error
            is_rate_limit = any(
                error.error_code in ['RESOURCE_TEMPORARILY_EXHAUSTED', 'RATE_EXCEEDED'] 
                for error in ex.failure.errors
            )
            
            if is_rate_limit and attempt < max_retries - 1:
                # Exponential backoff with jitter
                delay = base_delay * (2 ** attempt) + random.uniform(0, 1)
                delay = min(delay, 60)  # Cap at 60 seconds
                
                logging.warning(f"Rate limit hit, waiting {delay:.2f} seconds")
                time.sleep(delay)
                continue
            
            # Re-raise if not rate limit or max retries reached
            raise
        
        except Exception as ex:
            # Handle other exceptions
            if attempt == max_retries - 1:
                raise
            time.sleep(base_delay * (2 ** attempt))
```

---

## Common Errors and Solutions

### 1. CUSTOMER_NOT_ENABLED Error

```python
def handle_customer_not_enabled(customer_id):
    """Handle customer not enabled error"""
    return {
        'error': 'CUSTOMER_NOT_ENABLED',
        'message': f'Customer {customer_id} is not enabled for Google Ads API',
        'solution': 'Enable Google Ads API access in the Google Ads account settings',
        'customer_id': customer_id
    }
```

### 2. AUTHORIZATION_ERROR

```python
def handle_authorization_error(error):
    """Handle authorization errors"""
    missing_permissions = []
    
    for err in error.failure.errors:
        if err.error_code == 'AUTHORIZATION_ERROR':
            missing_permissions.append(err.details.permission)
    
    return {
        'error': 'AUTHORIZATION_ERROR',
        'message': 'Insufficient permissions for this operation',
        'missing_permissions': missing_permissions,
        'solution': 'Grant additional permissions in Google Ads account or OAuth scope'
    }
```

### 3. QUOTA_ERROR Handling

```python
def handle_quota_error(error):
    """Handle quota exceeded errors"""
    quota_info = {}
    
    for err in error.failure.errors:
        if err.error_code == 'QUOTA_ERROR':
            quota_info = {
                'quota_type': err.details.quota_type,
                'limit': err.details.limit,
                'usage': err.details.usage,
                'reset_time': err.details.reset_time
            }
    
    return {
        'error': 'QUOTA_ERROR',
        'message': 'API quota exceeded',
        'quota_details': quota_info,
        'solution': 'Wait for quota reset or request quota increase'
    }
```

### 4. VALIDATION_ERROR with Field-Specific Feedback

```python
def handle_validation_error_detailed(error):
    """Handle validation errors with field-specific feedback"""
    validation_errors = []
    
    for err in error.failure.errors:
        field_path = []
        if err.location:
            field_path = [element.field_name for element in err.location.field_path_elements]
        
        validation_errors.append({
            'field': '.'.join(field_path),
            'error_code': err.error_code,
            'message': err.message,
            'trigger': err.trigger,
            'suggested_fix': get_suggested_fix(err.error_code)
        })
    
    return {
        'error': 'VALIDATION_ERROR',
        'validation_errors': validation_errors,
        'request_id': error.request_id
    }

def get_suggested_fix(error_code):
    """Get suggested fix for common validation errors"""
    fixes = {
        'REQUIRED_FIELD_MISSING': 'Provide value for required field',
        'INVALID_VALUE': 'Check value format and constraints',
        'DUPLICATE_VALUE': 'Ensure unique values where required',
        'VALUE_NOT_IN_RANGE': 'Check value is within acceptable range',
        'STRING_TOO_LONG': 'Reduce string length to meet limits',
        'INVALID_URL': 'Provide valid URL format',
        'INVALID_EMAIL': 'Provide valid email format'
    }
    return fixes.get(error_code, 'Review field requirements and constraints')
```

---

## Performance Optimization

### Batch Operations for High Performance

```python
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

def create_batch_job_operations(client, customer_id, operations):
    """
    Create batch job for high-performance bulk operations
    """
    batch_job_service = client.get_service("BatchJobService")
    
    # Create batch job
    batch_job_operation = client.get_type("BatchJobOperation")
    batch_job = batch_job_service.mutate_batch_jobs(
        customer_id=customer_id,
        operations=[batch_job_operation]
    ).results[0]
    
    # Add operations to batch job
    batch_job_service.add_batch_job_operations(
        resource_name=batch_job.resource_name,
        sequence_token="",
        mutate_operations=operations
    )
    
    # Run batch job
    batch_job_service.run_batch_job(resource_name=batch_job.resource_name)
    
    return batch_job.resource_name

def monitor_batch_job(client, batch_job_resource_name):
    """Monitor batch job completion"""
    batch_job_service = client.get_service("BatchJobService")
    
    while True:
        batch_job = batch_job_service.get_batch_job(
            resource_name=batch_job_resource_name
        )
        
        if batch_job.status == client.enums.BatchJobStatusEnum.DONE:
            return batch_job
        elif batch_job.status == client.enums.BatchJobStatusEnum.FAILED:
            raise Exception(f"Batch job failed: {batch_job.failure_reason}")
        
        time.sleep(30)  # Poll every 30 seconds
```

### Efficient Data Retrieval

```python
def get_campaigns_efficiently(client, customer_id, page_size=1000):
    """
    Efficiently retrieve campaigns with pagination
    """
    ga_service = client.get_service("GoogleAdsService")
    
    query = """
        SELECT 
            campaign.id,
            campaign.name,
            campaign.status,
            campaign.advertising_channel_type,
            campaign.bidding_strategy_type,
            campaign.budget,
            metrics.impressions,
            metrics.clicks,
            metrics.cost_micros
        FROM campaign
        WHERE campaign.status != 'REMOVED'
        ORDER BY campaign.id
    """
    
    # Use page_size for efficient pagination
    search_request = client.get_type("SearchGoogleAdsRequest")
    search_request.customer_id = customer_id
    search_request.query = query
    search_request.page_size = page_size
    
    campaigns = []
    
    try:
        pages = ga_service.search(request=search_request)
        
        for page in pages:
            for row in page.results:
                campaigns.append({
                    'id': row.campaign.id,
                    'name': row.campaign.name,
                    'status': row.campaign.status.name,
                    'type': row.campaign.advertising_channel_type.name,
                    'bidding_strategy': row.campaign.bidding_strategy_type.name,
                    'budget': row.campaign.budget,
                    'impressions': row.metrics.impressions,
                    'clicks': row.metrics.clicks,
                    'cost': row.metrics.cost_micros / 1_000_000  # Convert to currency
                })
    
    except GoogleAdsException as ex:
        return handle_googleads_exception(ex)
    
    return campaigns
```

---

## Production Deployment Best Practices

### 1. Environment Configuration

```python
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class GoogleAdsConfig:
    """Configuration for Google Ads API"""
    developer_token: str
    client_id: str
    client_secret: str
    refresh_token: str
    login_customer_id: Optional[str] = None
    use_proto_plus: bool = True
    
    @classmethod
    def from_env(cls):
        """Load configuration from environment variables"""
        return cls(
            developer_token=os.getenv('GOOGLE_ADS_DEVELOPER_TOKEN'),
            client_id=os.getenv('GOOGLE_ADS_CLIENT_ID'),
            client_secret=os.getenv('GOOGLE_ADS_CLIENT_SECRET'),
            refresh_token=os.getenv('GOOGLE_ADS_REFRESH_TOKEN'),
            login_customer_id=os.getenv('GOOGLE_ADS_LOGIN_CUSTOMER_ID'),
            use_proto_plus=os.getenv('GOOGLE_ADS_USE_PROTO_PLUS', 'true').lower() == 'true'
        )
    
    def validate(self):
        """Validate configuration"""
        required_fields = ['developer_token', 'client_id', 'client_secret', 'refresh_token']
        missing_fields = [field for field in required_fields if not getattr(self, field)]
        
        if missing_fields:
            raise ValueError(f"Missing required configuration: {', '.join(missing_fields)}")
```

### 2. Logging and Monitoring

```python
import logging
import structlog
from google.ads.googleads.client import GoogleAdsClient

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.JSONRenderer()
    ],
    wrapper_class=structlog.stdlib.BoundLogger,
    logger_factory=structlog.stdlib.LoggerFactory(),
    cache_logger_on_first_use=True,
)

class GoogleAdsAPILogger:
    """Structured logging for Google Ads API operations"""
    
    def __init__(self, service_name: str):
        self.logger = structlog.get_logger(service_name)
    
    def log_request(self, customer_id: str, operation: str, **kwargs):
        """Log API request"""
        self.logger.info(
            "google_ads_api_request",
            customer_id=customer_id,
            operation=operation,
            **kwargs
        )
    
    def log_error(self, customer_id: str, operation: str, error: Exception, **kwargs):
        """Log API error"""
        if isinstance(error, GoogleAdsException):
            self.logger.error(
                "google_ads_api_error",
                customer_id=customer_id,
                operation=operation,
                request_id=error.request_id,
                error_count=len(error.failure.errors),
                error_codes=[err.error_code for err in error.failure.errors],
                **kwargs
            )
        else:
            self.logger.error(
                "unexpected_error",
                customer_id=customer_id,
                operation=operation,
                error_type=type(error).__name__,
                error_message=str(error),
                **kwargs
            )
    
    def log_success(self, customer_id: str, operation: str, **kwargs):
        """Log successful operation"""
        self.logger.info(
            "google_ads_api_success",
            customer_id=customer_id,
            operation=operation,
            **kwargs
        )
```

### 3. Health Checks and Monitoring

```python
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List

class GoogleAdsHealthChecker:
    """Health checker for Google Ads API connectivity"""
    
    def __init__(self, client: GoogleAdsClient):
        self.client = client
        self.last_check = {}
    
    async def check_api_health(self, customer_id: str) -> Dict:
        """Check API health for a customer"""
        try:
            # Simple query to test connectivity
            ga_service = self.client.get_service("GoogleAdsService")
            query = "SELECT customer.id FROM customer LIMIT 1"
            
            start_time = datetime.now()
            response = ga_service.search(
                customer_id=customer_id,
                query=query,
                page_size=1
            )
            
            # Consume the response
            list(response)
            
            end_time = datetime.now()
            response_time = (end_time - start_time).total_seconds()
            
            self.last_check[customer_id] = {
                'status': 'healthy',
                'response_time': response_time,
                'timestamp': datetime.now().isoformat()
            }
            
            return self.last_check[customer_id]
        
        except GoogleAdsException as ex:
            health_status = {
                'status': 'unhealthy',
                'error': 'google_ads_api_error',
                'error_codes': [err.error_code for err in ex.failure.errors],
                'request_id': ex.request_id,
                'timestamp': datetime.now().isoformat()
            }
            
            self.last_check[customer_id] = health_status
            return health_status
        
        except Exception as ex:
            health_status = {
                'status': 'unhealthy',
                'error': 'unexpected_error',
                'error_message': str(ex),
                'timestamp': datetime.now().isoformat()
            }
            
            self.last_check[customer_id] = health_status
            return health_status
    
    def get_health_summary(self) -> Dict:
        """Get overall health summary"""
        total_customers = len(self.last_check)
        healthy_customers = sum(1 for status in self.last_check.values() 
                              if status['status'] == 'healthy')
        
        return {
            'total_customers': total_customers,
            'healthy_customers': healthy_customers,
            'unhealthy_customers': total_customers - healthy_customers,
            'health_percentage': (healthy_customers / total_customers * 100) if total_customers > 0 else 0,
            'last_updated': datetime.now().isoformat()
        }
```

---

## Debugging and Troubleshooting

### 1. Request ID Tracking

```python
import uuid
from contextlib import contextmanager
from threading import local

class RequestContext:
    """Thread-local request context for tracking"""
    
    def __init__(self):
        self._local = local()
    
    def set_request_id(self, request_id: str):
        """Set request ID for current thread"""
        self._local.request_id = request_id
    
    def get_request_id(self) -> str:
        """Get request ID for current thread"""
        return getattr(self._local, 'request_id', str(uuid.uuid4()))

request_context = RequestContext()

@contextmanager
def track_request(operation_name: str):
    """Context manager for tracking requests"""
    request_id = str(uuid.uuid4())
    request_context.set_request_id(request_id)
    
    logger = structlog.get_logger()
    logger.info("request_started", 
                request_id=request_id, 
                operation=operation_name)
    
    try:
        yield request_id
        logger.info("request_completed", 
                   request_id=request_id, 
                   operation=operation_name)
    except Exception as ex:
        logger.error("request_failed", 
                    request_id=request_id, 
                    operation=operation_name,
                    error=str(ex))
        raise
```

### 2. Enhanced Error Reporting

```python
def create_error_report(exception: GoogleAdsException, context: dict = None):
    """Create comprehensive error report"""
    report = {
        'timestamp': datetime.now().isoformat(),
        'request_id': exception.request_id,
        'context': context or {},
        'errors': []
    }
    
    for error in exception.failure.errors:
        error_detail = {
            'error_code': error.error_code,
            'message': error.message,
            'trigger': error.trigger,
            'location': {
                'field_path': [],
                'operation_index': None
            }
        }
        
        if error.location:
            error_detail['location']['field_path'] = [
                {
                    'field_name': element.field_name,
                    'index': element.index
                }
                for element in error.location.field_path_elements
            ]
        
        report['errors'].append(error_detail)
    
    return report

def save_error_report(error_report: dict, customer_id: str):
    """Save error report for debugging"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"error_report_{customer_id}_{timestamp}.json"
    
    with open(f"logs/errors/{filename}", 'w') as f:
        json.dump(error_report, f, indent=2)
    
    # Also log to structured logging
    logger = structlog.get_logger()
    logger.error("error_report_saved", 
                filename=filename,
                customer_id=customer_id,
                error_count=len(error_report['errors']))
```

### 3. API Response Validation

```python
def validate_api_response(response, expected_type=None):
    """Validate API response structure"""
    if response is None:
        raise ValueError("API response is None")
    
    if expected_type and not isinstance(response, expected_type):
        raise TypeError(f"Expected {expected_type}, got {type(response)}")
    
    # Additional validation logic
    if hasattr(response, 'results') and len(response.results) == 0:
        logging.warning("API response contains no results")
    
    return response
```

---

## Complete Code Examples

### 1. Production-Ready API Client

```python
import os
import logging
import structlog
from typing import Optional, Dict, List
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException

class ProductionGoogleAdsClient:
    """Production-ready Google Ads API client with comprehensive error handling"""
    
    def __init__(self, config: GoogleAdsConfig):
        self.config = config
        self.config.validate()
        
        # Initialize client
        self.client = GoogleAdsClient.load_from_dict({
            'developer_token': config.developer_token,
            'client_id': config.client_id,
            'client_secret': config.client_secret,
            'refresh_token': config.refresh_token,
            'login_customer_id': config.login_customer_id,
            'use_proto_plus': config.use_proto_plus
        })
        
        # Initialize logging
        self.logger = GoogleAdsAPILogger("google_ads_client")
        
        # Initialize rate limiter
        self.rate_limiter = GoogleAdsRateLimiter()
    
    def get_campaigns(self, customer_id: str, include_removed: bool = False) -> Dict:
        """Get campaigns with comprehensive error handling"""
        with track_request("get_campaigns"):
            try:
                # Apply rate limiting
                self.rate_limiter.wait_if_needed(customer_id)
                
                # Log request
                self.logger.log_request(customer_id, "get_campaigns", 
                                      include_removed=include_removed)
                
                # Build query
                query = """
                    SELECT 
                        campaign.id,
                        campaign.name,
                        campaign.status,
                        campaign.advertising_channel_type,
                        campaign.bidding_strategy_type,
                        campaign.budget,
                        metrics.impressions,
                        metrics.clicks,
                        metrics.cost_micros
                    FROM campaign
                """
                
                if not include_removed:
                    query += " WHERE campaign.status != 'REMOVED'"
                
                query += " ORDER BY campaign.id"
                
                # Execute query
                ga_service = self.client.get_service("GoogleAdsService")
                response = ga_service.search(
                    customer_id=customer_id,
                    query=query,
                    page_size=1000
                )
                
                # Process results
                campaigns = []
                for row in response:
                    campaigns.append({
                        'id': row.campaign.id,
                        'name': row.campaign.name,
                        'status': row.campaign.status.name,
                        'type': row.campaign.advertising_channel_type.name,
                        'bidding_strategy': row.campaign.bidding_strategy_type.name,
                        'budget': row.campaign.budget,
                        'metrics': {
                            'impressions': row.metrics.impressions,
                            'clicks': row.metrics.clicks,
                            'cost': row.metrics.cost_micros / 1_000_000
                        }
                    })
                
                # Log success
                self.logger.log_success(customer_id, "get_campaigns", 
                                       campaign_count=len(campaigns))
                
                return {
                    'status': 'success',
                    'data': campaigns,
                    'total_count': len(campaigns)
                }
            
            except GoogleAdsException as ex:
                # Log error
                self.logger.log_error(customer_id, "get_campaigns", ex)
                
                # Create error report
                error_report = create_error_report(ex, {
                    'customer_id': customer_id,
                    'operation': 'get_campaigns',
                    'include_removed': include_removed
                })
                
                # Save error report
                save_error_report(error_report, customer_id)
                
                # Return structured error response
                return handle_googleads_exception(ex)
            
            except Exception as ex:
                # Log unexpected error
                self.logger.log_error(customer_id, "get_campaigns", ex)
                
                return {
                    'status': 'error',
                    'error': 'unexpected_error',
                    'message': str(ex)
                }
    
    def create_campaign(self, customer_id: str, campaign_data: Dict) -> Dict:
        """Create campaign with validation and error handling"""
        with track_request("create_campaign"):
            try:
                # Validate input
                if not campaign_data.get('name'):
                    return {
                        'status': 'validation_error',
                        'error': 'Campaign name is required'
                    }
                
                # Apply rate limiting
                self.rate_limiter.wait_if_needed(customer_id)
                
                # Log request
                self.logger.log_request(customer_id, "create_campaign", 
                                      campaign_name=campaign_data.get('name'))
                
                # Create campaign operation
                campaign_service = self.client.get_service("CampaignService")
                campaign_operation = self.client.get_type("CampaignOperation")
                
                campaign = campaign_operation.create
                campaign.name = campaign_data['name']
                campaign.advertising_channel_type = self.client.enums.AdvertisingChannelTypeEnum.SEARCH
                campaign.status = self.client.enums.CampaignStatusEnum.ENABLED
                
                # Set budget
                if campaign_data.get('budget_amount'):
                    campaign.campaign_budget = campaign_data['budget_amount']
                
                # Execute operation
                response = campaign_service.mutate_campaigns(
                    customer_id=customer_id,
                    operations=[campaign_operation]
                )
                
                # Log success
                created_campaign = response.results[0]
                self.logger.log_success(customer_id, "create_campaign",
                                       campaign_resource_name=created_campaign.resource_name)
                
                return {
                    'status': 'success',
                    'data': {
                        'resource_name': created_campaign.resource_name,
                        'id': created_campaign.resource_name.split('/')[-1]
                    }
                }
            
            except GoogleAdsException as ex:
                # Handle specific error types
                validation_errors = []
                for error in ex.failure.errors:
                    if error.error_code == 'REQUIRED_FIELD_MISSING':
                        validation_errors.append({
                            'field': error.location.field_path_elements[0].field_name if error.location else 'unknown',
                            'message': 'This field is required'
                        })
                    elif error.error_code == 'INVALID_VALUE':
                        validation_errors.append({
                            'field': error.location.field_path_elements[0].field_name if error.location else 'unknown',
                            'message': 'Invalid value provided'
                        })
                
                if validation_errors:
                    return {
                        'status': 'validation_error',
                        'validation_errors': validation_errors
                    }
                
                # Log error
                self.logger.log_error(customer_id, "create_campaign", ex)
                
                # Return structured error response
                return handle_googleads_exception(ex)
            
            except Exception as ex:
                # Log unexpected error
                self.logger.log_error(customer_id, "create_campaign", ex)
                
                return {
                    'status': 'error',
                    'error': 'unexpected_error',
                    'message': str(ex)
                }
```

### 2. Batch Processing with Error Handling

```python
def process_bulk_operations(client: ProductionGoogleAdsClient, 
                          customer_id: str, 
                          operations: List[Dict],
                          batch_size: int = 1000) -> Dict:
    """Process bulk operations with batch job and error handling"""
    
    with track_request("process_bulk_operations"):
        try:
            # Validate operations
            if not operations:
                return {
                    'status': 'validation_error',
                    'error': 'No operations provided'
                }
            
            # Split operations into batches
            batches = [operations[i:i+batch_size] for i in range(0, len(operations), batch_size)]
            
            results = []
            errors = []
            
            for batch_index, batch in enumerate(batches):
                try:
                    # Create batch job
                    batch_job_resource_name = create_batch_job_operations(
                        client.client, customer_id, batch
                    )
                    
                    # Monitor batch job
                    batch_job = monitor_batch_job(client.client, batch_job_resource_name)
                    
                    if batch_job.status == client.client.enums.BatchJobStatusEnum.DONE:
                        results.append({
                            'batch_index': batch_index,
                            'status': 'success',
                            'operations_count': len(batch)
                        })
                    else:
                        errors.append({
                            'batch_index': batch_index,
                            'status': 'failed',
                            'error': batch_job.failure_reason
                        })
                
                except GoogleAdsException as ex:
                    errors.append({
                        'batch_index': batch_index,
                        'status': 'error',
                        'error': handle_googleads_exception(ex)
                    })
                
                except Exception as ex:
                    errors.append({
                        'batch_index': batch_index,
                        'status': 'error',
                        'error': str(ex)
                    })
            
            return {
                'status': 'completed',
                'total_batches': len(batches),
                'successful_batches': len(results),
                'failed_batches': len(errors),
                'results': results,
                'errors': errors
            }
        
        except Exception as ex:
            return {
                'status': 'error',
                'error': 'bulk_processing_failed',
                'message': str(ex)
            }
```

---

## Summary

This comprehensive error handling guide provides:

1. **Structured Error Categorization**: Understanding different error types and appropriate handling strategies
2. **Production-Ready Code**: Complete Python implementations with error handling, logging, and monitoring
3. **Rate Limiting**: Advanced rate limiting strategies to prevent quota exhaustion
4. **Performance Optimization**: Batch processing and efficient data retrieval patterns
5. **Monitoring and Debugging**: Comprehensive logging, health checks, and error reporting
6. **Real-World Examples**: Complete code examples ready for production deployment

### Key Takeaways

- **Always categorize errors** and handle them appropriately based on their type
- **Implement comprehensive logging** with request IDs for debugging
- **Use structured error responses** to provide actionable feedback
- **Apply rate limiting** to prevent quota exhaustion
- **Monitor API health** and error trends for proactive issue resolution
- **Validate inputs** before making API calls to prevent validation errors
- **Use batch processing** for bulk operations to improve performance

This guide serves as a comprehensive reference for implementing robust error handling in production Google Ads API applications.