# Complete Google Ads Reporting and Analytics Guide

## Table of Contents
1. [Introduction to Google Ads Reporting API](#introduction)
2. [Google Ads Query Language (GAQL) Fundamentals](#gaql-fundamentals)
3. [Performance Metrics and KPIs](#performance-metrics)
4. [Search Term Reports](#search-term-reports)
5. [Campaign Performance Analysis](#campaign-performance)
6. [Custom Report Creation](#custom-reports)
7. [Real-time vs Historical Reporting](#realtime-vs-historical)
8. [Batch Processing](#batch-processing)
9. [Python Code Examples](#python-examples)
10. [Agency-Specific Reporting Patterns](#agency-patterns)
11. [Best Practices and Troubleshooting](#best-practices)

---

## 1. Introduction to Google Ads Reporting API {#introduction}

The Google Ads API provides flexible reporting capabilities to obtain performance data for all resources, from entire campaigns to specific keywords that triggered ads. The API uses Google Ads Query Language (GAQL) to query resources and their related attributes, segments, and metrics.

### Key Components:
- **GoogleAdsService**: Main service for running queries
- **Search vs SearchStream**: Two methods for retrieving data
- **GAQL**: Query language for data retrieval
- **Batch Processing**: For large-scale operations

### Data Freshness:
- Most account statistics (clicks, conversions, impressions) are delayed by less than 3 hours
- Conversions with attribution models other than "Last click" are delayed up to 15 hours
- Some metrics (auction insights, impression share, search terms) are updated a few times daily
- Data stability can vary - even 10 hours after midnight, reports may show increasing values

---

## 2. Google Ads Query Language (GAQL) Fundamentals {#gaql-fundamentals}

GAQL is the query language used to retrieve data from the Google Ads API. It follows a SQL-like syntax with specific Google Ads resources, segments, and metrics.

### Basic GAQL Structure:
```sql
SELECT [fields] FROM [resource] WHERE [conditions] ORDER BY [field] LIMIT [number]
```

### Core Components:

#### Resources
- `campaign`: Campaign-level data
- `ad_group`: Ad group-level data
- `keyword_view`: Keyword performance data
- `search_term_view`: Search term performance data
- `customer`: Account-level data

#### Segments
- `segments.date`: Date segmentation
- `segments.device`: Device segmentation
- `segments.hour`: Hour segmentation
- `segments.day_of_week`: Day of week segmentation

#### Metrics
- `metrics.impressions`: Total impressions
- `metrics.clicks`: Total clicks
- `metrics.ctr`: Click-through rate
- `metrics.cost_micros`: Cost in micros (divide by 1,000,000 for actual cost)
- `metrics.conversions`: Total conversions
- `metrics.average_cpc`: Average cost per click

### Example GAQL Queries:

#### Basic Campaign Performance:
```sql
SELECT
  campaign.id,
  campaign.name,
  campaign.status,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions
FROM campaign
WHERE campaign.status = 'ENABLED'
ORDER BY metrics.cost_micros DESC
LIMIT 100
```

#### Daily Performance with Segmentation:
```sql
SELECT
  campaign.name,
  segments.date,
  segments.device,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
  AND campaign.advertising_channel_type = 'SEARCH'
ORDER BY segments.date DESC
```

#### Keyword Performance:
```sql
SELECT
  campaign.name,
  ad_group.name,
  ad_group_criterion.keyword.text,
  ad_group_criterion.keyword.match_type,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.quality_score
FROM keyword_view
WHERE campaign.status = 'ENABLED'
  AND ad_group.status = 'ENABLED'
  AND ad_group_criterion.status = 'ENABLED'
ORDER BY metrics.impressions DESC
```

---

## 3. Performance Metrics and KPIs {#performance-metrics}

### Essential Performance Metrics:

#### Click Metrics:
- `metrics.clicks`: Total number of clicks
- `metrics.ctr`: Click-through rate (clicks/impressions)
- `metrics.average_cpc`: Average cost per click

#### Impression Metrics:
- `metrics.impressions`: Total impressions
- `metrics.absolute_top_impression_percentage`: Percentage of impressions at absolute top
- `metrics.top_impression_percentage`: Percentage of impressions at top
- `metrics.average_cpm`: Average cost per thousand impressions

#### Conversion Metrics:
- `metrics.conversions`: Total conversions
- `metrics.conversion_rate`: Conversion rate
- `metrics.cost_per_conversion`: Cost per conversion
- `metrics.conversion_value`: Total conversion value

#### Cost Metrics:
- `metrics.cost_micros`: Total cost in micros
- `metrics.average_cpc`: Average cost per click
- `metrics.average_cpm`: Average cost per thousand impressions

#### Quality Metrics:
- `metrics.quality_score`: Quality Score (1-10)
- `metrics.search_impression_share`: Search impression share percentage
- `metrics.search_lost_impression_share_budget`: Lost impression share due to budget
- `metrics.search_lost_impression_share_rank`: Lost impression share due to rank

### Key KPIs for Agency Reporting:

#### 1. Return on Ad Spend (ROAS)
```sql
SELECT
  campaign.name,
  metrics.cost_micros,
  metrics.conversion_value,
  (metrics.conversion_value / (metrics.cost_micros / 1000000)) AS roas
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
```

#### 2. Quality Score Distribution
```sql
SELECT
  metrics.quality_score,
  COUNT(*) as keyword_count,
  AVG(metrics.ctr) as avg_ctr,
  AVG(metrics.cost_micros) as avg_cost
FROM keyword_view
WHERE campaign.status = 'ENABLED'
GROUP BY metrics.quality_score
ORDER BY metrics.quality_score DESC
```

#### 3. Device Performance Comparison
```sql
SELECT
  segments.device,
  SUM(metrics.impressions) as total_impressions,
  SUM(metrics.clicks) as total_clicks,
  AVG(metrics.ctr) as avg_ctr,
  SUM(metrics.cost_micros) as total_cost
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
GROUP BY segments.device
ORDER BY total_cost DESC
```

---

## 4. Search Term Reports {#search-term-reports}

Search term reports show the actual search queries that triggered your ads. This is crucial for identifying profitable keywords and negative keywords.

### Search Term View Resource:
```sql
SELECT
  campaign.name,
  ad_group.name,
  search_term_view.search_term,
  search_term_view.status,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions
FROM search_term_view
WHERE segments.date DURING LAST_30_DAYS
  AND metrics.impressions > 0
ORDER BY metrics.cost_micros DESC
```

### Advanced Search Term Analysis:

#### High-Spending Low-Converting Terms:
```sql
SELECT
  search_term_view.search_term,
  SUM(metrics.cost_micros) as total_cost,
  SUM(metrics.conversions) as total_conversions,
  AVG(metrics.ctr) as avg_ctr
FROM search_term_view
WHERE segments.date DURING LAST_30_DAYS
  AND metrics.cost_micros > 1000000  -- More than $1 spent
GROUP BY search_term_view.search_term
HAVING total_conversions = 0
ORDER BY total_cost DESC
```

#### Profitable Search Terms for Keyword Expansion:
```sql
SELECT
  search_term_view.search_term,
  SUM(metrics.impressions) as total_impressions,
  SUM(metrics.clicks) as total_clicks,
  SUM(metrics.conversions) as total_conversions,
  (SUM(metrics.conversion_value) / SUM(metrics.cost_micros) * 1000000) as roas
FROM search_term_view
WHERE segments.date DURING LAST_30_DAYS
  AND metrics.conversions > 0
GROUP BY search_term_view.search_term
HAVING roas > 3.0  -- 3x ROAS threshold
ORDER BY total_conversions DESC
```

---

## 5. Campaign Performance Analysis {#campaign-performance}

### Campaign-Level Performance:
```sql
SELECT
  campaign.id,
  campaign.name,
  campaign.advertising_channel_type,
  campaign.status,
  campaign.budget_amount_micros,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions,
  metrics.conversion_rate,
  metrics.search_impression_share
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
ORDER BY metrics.cost_micros DESC
```

### Ad Group Performance:
```sql
SELECT
  campaign.name,
  ad_group.id,
  ad_group.name,
  ad_group.status,
  ad_group.target_cpa_micros,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions,
  metrics.quality_score
FROM ad_group
WHERE segments.date DURING LAST_30_DAYS
  AND campaign.status = 'ENABLED'
  AND ad_group.status = 'ENABLED'
ORDER BY metrics.cost_micros DESC
```

### Ad Performance:
```sql
SELECT
  campaign.name,
  ad_group.name,
  ad_group_ad.ad.id,
  ad_group_ad.ad.final_urls,
  ad_group_ad.ad.responsive_search_ad.headlines,
  ad_group_ad.ad.responsive_search_ad.descriptions,
  ad_group_ad.status,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions
FROM ad_group_ad
WHERE segments.date DURING LAST_30_DAYS
  AND campaign.status = 'ENABLED'
  AND ad_group.status = 'ENABLED'
  AND ad_group_ad.status = 'ENABLED'
ORDER BY metrics.impressions DESC
```

---

## 6. Custom Report Creation {#custom-reports}

### Using Google Ads API Report Fetcher (GAARF):

#### Installation:
```bash
pip install google-ads-api-report-fetcher
```

#### Basic Usage:
```python
from google_ads_api_report_fetcher import GoogleAdsReportFetcher
from google.ads.googleads.client import GoogleAdsClient

# Initialize client
client = GoogleAdsClient.load_from_storage()

# Create report fetcher
report_fetcher = GoogleAdsReportFetcher(client)

# Define query
query = """
SELECT
  campaign.name,
  segments.date,
  metrics.impressions,
  metrics.clicks,
  metrics.cost_micros
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
"""

# Execute query
results = report_fetcher.fetch_report(
    customer_id="**********",
    query=query
)

# Process results
for row in results:
    print(f"Campaign: {row.campaign.name}")
    print(f"Date: {row.segments.date}")
    print(f"Impressions: {row.metrics.impressions}")
    print(f"Clicks: {row.metrics.clicks}")
    print(f"Cost: ${row.metrics.cost_micros / 1000000:.2f}")
```

### Custom Report Templates:

#### Weekly Performance Summary:
```sql
SELECT
  customer.descriptive_name,
  campaign.name,
  segments.week,
  SUM(metrics.impressions) as weekly_impressions,
  SUM(metrics.clicks) as weekly_clicks,
  AVG(metrics.ctr) as avg_ctr,
  SUM(metrics.cost_micros) as weekly_cost,
  SUM(metrics.conversions) as weekly_conversions
FROM campaign
WHERE segments.date DURING LAST_4_WEEKS
GROUP BY customer.descriptive_name, campaign.name, segments.week
ORDER BY weekly_cost DESC
```

#### Geographic Performance Report:
```sql
SELECT
  campaign.name,
  geographic_view.country_criterion_id,
  geographic_view.location_type,
  metrics.impressions,
  metrics.clicks,
  metrics.ctr,
  metrics.cost_micros,
  metrics.conversions
FROM geographic_view
WHERE segments.date DURING LAST_30_DAYS
  AND metrics.impressions > 100
ORDER BY metrics.cost_micros DESC
```

---

## 7. Real-time vs Historical Reporting {#realtime-vs-historical}

### Data Freshness Timeline:

#### Real-time Data (< 3 hours):
- Click metrics
- Impression metrics
- Cost data
- Basic performance metrics

#### Near Real-time Data (3-15 hours):
- Conversion data with attribution models
- Quality Score updates
- Bid adjustments

#### Batch Updated Data (Daily):
- Auction insights
- Impression share metrics
- Search term reports
- Historical metrics

### Data Stability Considerations:

#### Important Notes:
- Data may continue to change up to 10 hours after midnight in the account's timezone
- Low-traffic accounts may see up to 10% variation in final numbers
- High-traffic accounts typically see less than 0.1% variation
- Always consider data stability when building automated reporting systems

### Search vs SearchStream Methods:

#### Search Method (Paginated):
```python
def get_campaign_data_paginated(client, customer_id, query):
    ga_service = client.get_service("GoogleAdsService")
    request = client.get_type("SearchGoogleAdsRequest")
    request.customer_id = customer_id
    request.query = query
    request.page_size = 1000
    
    results = []
    page_token = None
    
    while True:
        if page_token:
            request.page_token = page_token
            
        response = ga_service.search(request=request)
        
        for row in response.results:
            results.append(row)
            
        page_token = response.next_page_token
        if not page_token:
            break
            
    return results
```

#### SearchStream Method (Streaming):
```python
def get_campaign_data_stream(client, customer_id, query):
    ga_service = client.get_service("GoogleAdsService")
    request = client.get_type("SearchGoogleAdsStreamRequest")
    request.customer_id = customer_id
    request.query = query
    
    results = []
    stream = ga_service.search_stream(request=request)
    
    for batch in stream:
        for row in batch.results:
            results.append(row)
            
    return results
```

### Recommendations:
- Use **SearchStream** for most reporting scenarios (better performance)
- Use **Search** when you need specific pagination control
- For reports under 10,000 rows, performance difference is minimal
- SearchStream eliminates round-trip network time for large reports

---

## 8. Batch Processing {#batch-processing}

### When to Use Batch Processing:
- Large-scale operations (>1000 operations)
- Mixed operation types (campaigns, ad groups, keywords)
- Operations that can tolerate asynchronous processing
- Bulk data imports/exports

### Batch Job Example:
```python
def create_batch_job(client, customer_id, operations):
    batch_job_service = client.get_service("BatchJobService")
    
    # Create batch job
    batch_job_operation = client.get_type("BatchJobOperation")
    batch_job = batch_job_operation.create
    batch_job.customer_id = customer_id
    
    response = batch_job_service.mutate_batch_job(
        customer_id=customer_id,
        operation=batch_job_operation
    )
    
    batch_job_resource_name = response.result.resource_name
    
    # Add operations to batch job
    add_operations_request = client.get_type("AddBatchJobOperationsRequest")
    add_operations_request.resource_name = batch_job_resource_name
    add_operations_request.mutate_operations = operations
    
    batch_job_service.add_batch_job_operations(
        request=add_operations_request
    )
    
    # Run batch job
    batch_job_service.run_batch_job(
        resource_name=batch_job_resource_name
    )
    
    return batch_job_resource_name
```

### Batch vs Bulk Operations:

#### Batch Processing (Asynchronous):
- Up to 1,000,000 operations per job
- Mixed operation types
- Automatic retry on transient errors
- Temporary ID support
- Better for large-scale operations

#### Bulk Operations (Synchronous):
- Up to 5,000 operations per request
- Single operation type
- Immediate response
- Simpler implementation
- Better for smaller operations

---

## 9. Python Code Examples {#python-examples}

### Complete Reporting Class:
```python
import asyncio
from google.ads.googleads.client import GoogleAdsClient
from google.ads.googleads.errors import GoogleAdsException
import pandas as pd
from datetime import datetime, timedelta

class GoogleAdsReporter:
    def __init__(self, client, customer_id):
        self.client = client
        self.customer_id = customer_id
        self.ga_service = client.get_service("GoogleAdsService")
    
    def execute_query(self, query, use_stream=True):
        """Execute a GAQL query and return results"""
        try:
            if use_stream:
                return self._execute_stream_query(query)
            else:
                return self._execute_paginated_query(query)
        except GoogleAdsException as ex:
            print(f"Request failed with status {ex.error.code().name}")
            for error in ex.failure.errors:
                print(f"Error: {error.message}")
            return []
    
    def _execute_stream_query(self, query):
        """Execute query using SearchStream"""
        request = self.client.get_type("SearchGoogleAdsStreamRequest")
        request.customer_id = self.customer_id
        request.query = query
        
        results = []
        stream = self.ga_service.search_stream(request=request)
        
        for batch in stream:
            for row in batch.results:
                results.append(row)
        
        return results
    
    def _execute_paginated_query(self, query):
        """Execute query using Search with pagination"""
        request = self.client.get_type("SearchGoogleAdsRequest")
        request.customer_id = self.customer_id
        request.query = query
        request.page_size = 1000
        
        results = []
        page_token = None
        
        while True:
            if page_token:
                request.page_token = page_token
            
            response = self.ga_service.search(request=request)
            
            for row in response.results:
                results.append(row)
            
            page_token = response.next_page_token
            if not page_token:
                break
        
        return results
    
    def get_campaign_performance(self, date_range="LAST_30_DAYS"):
        """Get campaign performance data"""
        query = f"""
        SELECT
          campaign.id,
          campaign.name,
          campaign.status,
          campaign.advertising_channel_type,
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.cost_micros,
          metrics.conversions,
          metrics.conversion_rate,
          metrics.search_impression_share
        FROM campaign
        WHERE segments.date DURING {date_range}
        ORDER BY metrics.cost_micros DESC
        """
        
        results = self.execute_query(query)
        
        # Convert to DataFrame for easier analysis
        data = []
        for row in results:
            data.append({
                'campaign_id': row.campaign.id,
                'campaign_name': row.campaign.name,
                'status': row.campaign.status.name,
                'channel_type': row.campaign.advertising_channel_type.name,
                'impressions': row.metrics.impressions,
                'clicks': row.metrics.clicks,
                'ctr': row.metrics.ctr,
                'cost': row.metrics.cost_micros / 1000000,
                'conversions': row.metrics.conversions,
                'conversion_rate': row.metrics.conversion_rate,
                'impression_share': row.metrics.search_impression_share
            })
        
        return pd.DataFrame(data)
    
    def get_search_terms(self, date_range="LAST_30_DAYS", min_cost=0):
        """Get search term performance data"""
        query = f"""
        SELECT
          campaign.name,
          ad_group.name,
          search_term_view.search_term,
          search_term_view.status,
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.cost_micros,
          metrics.conversions,
          metrics.conversion_rate
        FROM search_term_view
        WHERE segments.date DURING {date_range}
          AND metrics.cost_micros > {min_cost * 1000000}
        ORDER BY metrics.cost_micros DESC
        """
        
        results = self.execute_query(query)
        
        data = []
        for row in results:
            data.append({
                'campaign_name': row.campaign.name,
                'ad_group_name': row.ad_group.name,
                'search_term': row.search_term_view.search_term,
                'status': row.search_term_view.status.name,
                'impressions': row.metrics.impressions,
                'clicks': row.metrics.clicks,
                'ctr': row.metrics.ctr,
                'cost': row.metrics.cost_micros / 1000000,
                'conversions': row.metrics.conversions,
                'conversion_rate': row.metrics.conversion_rate
            })
        
        return pd.DataFrame(data)
    
    def get_keyword_performance(self, date_range="LAST_30_DAYS"):
        """Get keyword performance data"""
        query = f"""
        SELECT
          campaign.name,
          ad_group.name,
          ad_group_criterion.keyword.text,
          ad_group_criterion.keyword.match_type,
          ad_group_criterion.quality_info.quality_score,
          metrics.impressions,
          metrics.clicks,
          metrics.ctr,
          metrics.cost_micros,
          metrics.conversions,
          metrics.average_cpc
        FROM keyword_view
        WHERE segments.date DURING {date_range}
          AND campaign.status = 'ENABLED'
          AND ad_group.status = 'ENABLED'
          AND ad_group_criterion.status = 'ENABLED'
        ORDER BY metrics.impressions DESC
        """
        
        results = self.execute_query(query)
        
        data = []
        for row in results:
            data.append({
                'campaign_name': row.campaign.name,
                'ad_group_name': row.ad_group.name,
                'keyword': row.ad_group_criterion.keyword.text,
                'match_type': row.ad_group_criterion.keyword.match_type.name,
                'quality_score': row.ad_group_criterion.quality_info.quality_score,
                'impressions': row.metrics.impressions,
                'clicks': row.metrics.clicks,
                'ctr': row.metrics.ctr,
                'cost': row.metrics.cost_micros / 1000000,
                'conversions': row.metrics.conversions,
                'avg_cpc': row.metrics.average_cpc / 1000000
            })
        
        return pd.DataFrame(data)
    
    def get_negative_keyword_opportunities(self, date_range="LAST_30_DAYS", min_cost=5):
        """Find potential negative keyword opportunities"""
        query = f"""
        SELECT
          search_term_view.search_term,
          campaign.name,
          ad_group.name,
          SUM(metrics.cost_micros) as total_cost,
          SUM(metrics.conversions) as total_conversions,
          AVG(metrics.ctr) as avg_ctr
        FROM search_term_view
        WHERE segments.date DURING {date_range}
          AND metrics.cost_micros > {min_cost * 1000000}
        GROUP BY search_term_view.search_term, campaign.name, ad_group.name
        HAVING total_conversions = 0
        ORDER BY total_cost DESC
        """
        
        results = self.execute_query(query)
        
        data = []
        for row in results:
            data.append({
                'search_term': row.search_term_view.search_term,
                'campaign_name': row.campaign.name,
                'ad_group_name': row.ad_group.name,
                'total_cost': row.metrics.cost_micros / 1000000,
                'total_conversions': row.metrics.conversions,
                'avg_ctr': row.metrics.ctr
            })
        
        return pd.DataFrame(data)

# Usage Example:
def main():
    # Initialize client
    client = GoogleAdsClient.load_from_storage()
    customer_id = "**********"
    
    # Create reporter
    reporter = GoogleAdsReporter(client, customer_id)
    
    # Get campaign performance
    campaigns = reporter.get_campaign_performance()
    print("Top 10 campaigns by cost:")
    print(campaigns.head(10))
    
    # Get search terms
    search_terms = reporter.get_search_terms(min_cost=1)
    print("\nTop search terms by cost:")
    print(search_terms.head(10))
    
    # Get negative keyword opportunities
    negative_opportunities = reporter.get_negative_keyword_opportunities()
    print("\nNegative keyword opportunities:")
    print(negative_opportunities.head(10))

if __name__ == "__main__":
    main()
```

### Asynchronous Reporting for Multiple Accounts:
```python
import asyncio
from concurrent.futures import ThreadPoolExecutor

class MultiAccountReporter:
    def __init__(self, client, customer_ids):
        self.client = client
        self.customer_ids = customer_ids
    
    async def get_all_account_performance(self, date_range="LAST_30_DAYS"):
        """Get performance data for all accounts asynchronously"""
        loop = asyncio.get_event_loop()
        
        with ThreadPoolExecutor(max_workers=10) as executor:
            tasks = []
            for customer_id in self.customer_ids:
                task = loop.run_in_executor(
                    executor,
                    self._get_account_performance,
                    customer_id,
                    date_range
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
        
        # Combine results
        all_data = pd.concat(results, ignore_index=True)
        return all_data
    
    def _get_account_performance(self, customer_id, date_range):
        """Get performance data for a single account"""
        reporter = GoogleAdsReporter(self.client, customer_id)
        data = reporter.get_campaign_performance(date_range)
        data['customer_id'] = customer_id
        return data

# Usage:
async def multi_account_report():
    client = GoogleAdsClient.load_from_storage()
    customer_ids = ["**********", "**********", "**********"]
    
    multi_reporter = MultiAccountReporter(client, customer_ids)
    all_performance = await multi_reporter.get_all_account_performance()
    
    print("Combined performance across all accounts:")
    print(all_performance.groupby('customer_id').agg({
        'impressions': 'sum',
        'clicks': 'sum',
        'cost': 'sum',
        'conversions': 'sum'
    }))

# Run the async function
asyncio.run(multi_account_report())
```

---

## 10. Agency-Specific Reporting Patterns {#agency-patterns}

### Client Dashboard Reports:

#### Monthly Performance Summary:
```python
def generate_monthly_client_report(reporter, client_name, start_date, end_date):
    """Generate a comprehensive monthly report for a client"""
    
    # Campaign performance
    campaigns = reporter.get_campaign_performance(f"{start_date}_{end_date}")
    
    # Search terms analysis
    search_terms = reporter.get_search_terms(f"{start_date}_{end_date}")
    
    # Keyword performance
    keywords = reporter.get_keyword_performance(f"{start_date}_{end_date}")
    
    # Generate insights
    insights = {
        'total_spend': campaigns['cost'].sum(),
        'total_clicks': campaigns['clicks'].sum(),
        'total_impressions': campaigns['impressions'].sum(),
        'avg_ctr': campaigns['ctr'].mean(),
        'total_conversions': campaigns['conversions'].sum(),
        'top_campaign': campaigns.loc[campaigns['cost'].idxmax(), 'campaign_name'],
        'top_search_term': search_terms.loc[search_terms['cost'].idxmax(), 'search_term'],
        'quality_score_avg': keywords['quality_score'].mean()
    }
    
    return {
        'client_name': client_name,
        'period': f"{start_date} to {end_date}",
        'summary': insights,
        'campaigns': campaigns,
        'search_terms': search_terms,
        'keywords': keywords
    }
```

#### Agency Performance Dashboard:
```python
def generate_agency_dashboard(multi_reporter, date_range="LAST_30_DAYS"):
    """Generate agency-wide performance dashboard"""
    
    # Get data for all accounts
    all_data = asyncio.run(multi_reporter.get_all_account_performance(date_range))
    
    # Agency-level KPIs
    agency_kpis = {
        'total_accounts': len(all_data['customer_id'].unique()),
        'total_campaigns': len(all_data),
        'total_spend': all_data['cost'].sum(),
        'total_clicks': all_data['clicks'].sum(),
        'total_impressions': all_data['impressions'].sum(),
        'avg_ctr': all_data['ctr'].mean(),
        'total_conversions': all_data['conversions'].sum(),
        'avg_cpc': all_data['cost'].sum() / all_data['clicks'].sum() if all_data['clicks'].sum() > 0 else 0,
        'conversion_rate': all_data['conversions'].sum() / all_data['clicks'].sum() if all_data['clicks'].sum() > 0 else 0
    }
    
    # Top performing accounts
    account_performance = all_data.groupby('customer_id').agg({
        'cost': 'sum',
        'clicks': 'sum',
        'impressions': 'sum',
        'conversions': 'sum'
    }).reset_index()
    
    account_performance['ctr'] = account_performance['clicks'] / account_performance['impressions']
    account_performance['conversion_rate'] = account_performance['conversions'] / account_performance['clicks']
    
    return {
        'agency_kpis': agency_kpis,
        'account_performance': account_performance,
        'campaign_details': all_data
    }
```

### Automated Alert System:
```python
def check_performance_alerts(reporter, thresholds):
    """Check for performance alerts based on thresholds"""
    alerts = []
    
    # Get recent performance
    campaigns = reporter.get_campaign_performance("LAST_7_DAYS")
    
    # Check for high spend, low conversion campaigns
    high_spend_low_conv = campaigns[
        (campaigns['cost'] > thresholds['min_spend']) & 
        (campaigns['conversions'] < thresholds['min_conversions'])
    ]
    
    for _, campaign in high_spend_low_conv.iterrows():
        alerts.append({
            'type': 'HIGH_SPEND_LOW_CONVERSION',
            'campaign': campaign['campaign_name'],
            'spend': campaign['cost'],
            'conversions': campaign['conversions'],
            'severity': 'HIGH'
        })
    
    # Check for low CTR campaigns
    low_ctr_campaigns = campaigns[campaigns['ctr'] < thresholds['min_ctr']]
    
    for _, campaign in low_ctr_campaigns.iterrows():
        alerts.append({
            'type': 'LOW_CTR',
            'campaign': campaign['campaign_name'],
            'ctr': campaign['ctr'],
            'severity': 'MEDIUM'
        })
    
    # Check for low impression share
    low_impression_share = campaigns[
        campaigns['impression_share'] < thresholds['min_impression_share']
    ]
    
    for _, campaign in low_impression_share.iterrows():
        alerts.append({
            'type': 'LOW_IMPRESSION_SHARE',
            'campaign': campaign['campaign_name'],
            'impression_share': campaign['impression_share'],
            'severity': 'LOW'
        })
    
    return alerts

# Usage:
thresholds = {
    'min_spend': 100,
    'min_conversions': 1,
    'min_ctr': 0.02,
    'min_impression_share': 0.5
}

alerts = check_performance_alerts(reporter, thresholds)
for alert in alerts:
    print(f"ALERT: {alert['type']} - {alert['campaign']}")
```

---

## 11. Best Practices and Troubleshooting {#best-practices}

### Best Practices:

#### 1. Query Optimization:
- Use appropriate date ranges to avoid large data sets
- Include only necessary fields in SELECT statements
- Use WHERE clauses to filter data at the API level
- Consider using SearchStream for better performance

#### 2. Error Handling:
```python
def safe_execute_query(reporter, query, max_retries=3):
    """Execute query with retry logic"""
    for attempt in range(max_retries):
        try:
            return reporter.execute_query(query)
        except GoogleAdsException as ex:
            if attempt == max_retries - 1:
                raise
            
            # Check if error is retryable
            if ex.error.code().name in ['INTERNAL_ERROR', 'DEADLINE_EXCEEDED']:
                time.sleep(2 ** attempt)  # Exponential backoff
                continue
            else:
                raise
```

#### 3. Rate Limiting:
```python
import time
from functools import wraps

def rate_limit(calls_per_minute=60):
    """Decorator to rate limit API calls"""
    min_interval = 60.0 / calls_per_minute
    last_called = [0.0]
    
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            elapsed = time.time() - last_called[0]
            left_to_wait = min_interval - elapsed
            if left_to_wait > 0:
                time.sleep(left_to_wait)
            ret = func(*args, **kwargs)
            last_called[0] = time.time()
            return ret
        return wrapper
    return decorator

@rate_limit(calls_per_minute=30)
def execute_query_with_rate_limit(reporter, query):
    return reporter.execute_query(query)
```

#### 4. Data Validation:
```python
def validate_metrics(data):
    """Validate metrics data for consistency"""
    issues = []
    
    # Check for negative values
    if (data['cost'] < 0).any():
        issues.append("Negative cost values found")
    
    # Check CTR calculation
    calculated_ctr = data['clicks'] / data['impressions']
    if not np.allclose(data['ctr'], calculated_ctr, rtol=0.01):
        issues.append("CTR values don't match calculated values")
    
    # Check for missing data
    if data.isnull().any().any():
        issues.append("Missing data found")
    
    return issues
```

### Common Issues and Solutions:

#### 1. GAQL Query Errors:
```python
# Common GAQL mistakes and fixes:

# ❌ Wrong: Missing segments.date when filtering by date
query = """
SELECT campaign.name, metrics.clicks
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
"""

# ✅ Correct: Include segments.date in SELECT when filtering by date
query = """
SELECT campaign.name, segments.date, metrics.clicks
FROM campaign
WHERE segments.date DURING LAST_30_DAYS
"""
```

#### 2. Resource Compatibility:
```python
# ❌ Wrong: Mixing incompatible resources
query = """
SELECT campaign.name, ad_group.name, search_term_view.search_term
FROM campaign
"""

# ✅ Correct: Use appropriate resource
query = """
SELECT campaign.name, ad_group.name, search_term_view.search_term
FROM search_term_view
"""
```

#### 3. Data Type Handling:
```python
def process_google_ads_data(row):
    """Properly handle Google Ads data types"""
    return {
        'campaign_id': str(row.campaign.id),  # Convert to string
        'cost': row.metrics.cost_micros / 1000000,  # Convert micros to actual cost
        'ctr': float(row.metrics.ctr),  # Ensure float type
        'date': row.segments.date.strftime('%Y-%m-%d'),  # Format date
        'impressions': int(row.metrics.impressions)  # Ensure integer
    }
```

### Performance Monitoring:
```python
import time
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def monitor_query_performance(func):
    """Decorator to monitor query performance"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            execution_time = time.time() - start_time
            logger.info(f"Query executed in {execution_time:.2f} seconds")
            return result
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"Query failed after {execution_time:.2f} seconds: {str(e)}")
            raise
    return wrapper
```

---

## Conclusion

This comprehensive guide covers all aspects of Google Ads Reporting and Analytics API, from basic GAQL queries to advanced agency-specific reporting patterns. Key takeaways:

1. **Use GAQL effectively** - Understanding the query language is crucial for extracting meaningful data
2. **Choose the right method** - SearchStream is generally preferred for better performance
3. **Handle data properly** - Consider data freshness, stability, and proper type conversion
4. **Implement proper error handling** - Include retry logic and validation
5. **Optimize for your use case** - Different scenarios (real-time vs historical, single vs multi-account) require different approaches

For agency use cases, focus on:
- Multi-account reporting patterns
- Automated alert systems
- Client-specific dashboard generation
- Performance monitoring and optimization

Remember to always test queries with small date ranges first, implement proper error handling, and consider the data freshness timeline when building automated systems.