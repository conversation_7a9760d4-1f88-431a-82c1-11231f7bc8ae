# Google Ads API Troubleshooting Guide

## Issue: "No Data Showing" After OAuth Connection

### Common Causes & Solutions

#### 1. Developer Token Access Level Mismatch

**Symptom**: OAuth succeeds but API calls return empty data or permission errors.

**Cause**: Your developer token has **Basic Access** but you might be:
- Using a test manager account ID in configuration
- Trying to access test accounts with a production token
- Missing proper login_customer_id configuration

**Solution**:
```python
# Ensure you're using your production MCC ID, not test account
GOOGLE_ADS_LOGIN_CUSTOMER_ID=**********  # Without dashes
```

#### 2. Manager Account (MCC) Configuration Issues

**Symptom**: Can authenticate but no accounts show up.

**Common Issues**:
1. **Wrong Customer ID Format**:
   ```python
   # ❌ Wrong
   login_customer_id = "310-946-3592"  # With dashes
   
   # ✅ Correct
   login_customer_id = "**********"    # Without dashes
   ```

2. **Using Sub-Account ID Instead of MCC**:
   - Always use your Manager Account ID as login_customer_id
   - The API needs MCC-level access to list all accounts

3. **Account Not Linked to MCC**:
   - Verify the OAuth user has access to the MCC account
   - Check in Google Ads UI: Tools & Settings > Access and security

#### 3. OAuth Scope Issues

**Symptom**: Authentication works but API calls fail.

**Solution**: Ensure OAuth uses correct scope:
```python
scopes = ['https://www.googleapis.com/auth/adwords']  # Must be 'adwords', not 'ads'
```

#### 4. API Response Handling

**Symptom**: API returns data but frontend shows empty.

**Debug Steps**:
```python
# Add detailed logging in google_ads_real.py
logger.info(f"API Response: {response}")
logger.info(f"Accessible customers: {accessible_customers}")
logger.info(f"Formatted accounts: {accounts}")
```

#### 5. Common Permission Errors

**PERMISSION_DENIED**: 
- Developer token not approved for production access
- Wrong login_customer_id
- User doesn't have access to the MCC

**GRPC_ERROR**:
- Usually indicates test account being accessed with production token
- Solution: Use only production accounts with Basic Access token

### Step-by-Step Debugging Process

#### 1. Verify Configuration
```bash
# Check backend/.env
cat backend/.env | grep GOOGLE_ADS

# Should show:
# GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jlhjqA
# GOOGLE_ADS_LOGIN_CUSTOMER_ID=**********
```

#### 2. Test OAuth Flow
```bash
# Start backend server with debug logging
cd backend
source venv/bin/activate
export PYTHONUNBUFFERED=1
python3 -u run_server.py 2>&1 | tee debug.log
```

#### 3. Monitor API Calls
```python
# Add to google_ads_real.py for debugging
def list_accessible_customers(self, refresh_token: str) -> List[str]:
    try:
        client = self.get_google_ads_client(refresh_token)
        logger.info(f"Client created with developer token: {settings.GOOGLE_ADS_DEVELOPER_TOKEN[:10]}...")
        
        customer_service = client.get_service("CustomerService")
        accessible_customers = customer_service.list_accessible_customers()
        
        logger.info(f"Raw response: {accessible_customers}")
        logger.info(f"Resource names: {accessible_customers.resource_names}")
        
        return [customer for customer in accessible_customers.resource_names]
    except GoogleAdsException as ex:
        logger.error(f"GoogleAdsException: {ex.failure}")
        logger.error(f"Request ID: {ex.request_id}")
        for error in ex.failure.errors:
            logger.error(f"Error code: {error.error_code}")
            logger.error(f"Error message: {error.message}")
        raise
```

#### 4. Check Frontend Console
```javascript
// In frontend, check browser console
// Network tab: Look for /api/google-ads/accounts
// Response should contain account data
```

#### 5. Verify Database Storage
```sql
-- Check if tokens are stored
SELECT * FROM user_google_ads_tokens WHERE user_id = '<your-user-id>';
```

### Quick Fixes Checklist

- [ ] Remove dashes from customer IDs
- [ ] Use MCC ID (310-946-3592 → **********) as login_customer_id
- [ ] Verify developer token is exactly: USJoZ_CN_pYY2MP-jlhjqA
- [ ] Check OAuth user has access to MCC account 310-946-3592
- [ ] Clear browser cache and re-authenticate
- [ ] Check backend logs for specific error messages
- [ ] Ensure using production accounts, not test accounts

### Testing API Access

Create a test script to verify API access:

```python
# test_api.py
from google.ads.googleads.client import GoogleAdsClient

config = {
    "developer_token": "USJoZ_CN_pYY2MP-jlhjqA",
    "login_customer_id": "**********",
    "client_id": "<your-oauth-client-id>",
    "client_secret": "<your-oauth-client-secret>",
    "refresh_token": "<your-refresh-token>"
}

client = GoogleAdsClient.load_from_dict(config)
customer_service = client.get_service("CustomerService")

try:
    response = customer_service.list_accessible_customers()
    print(f"Accessible customers: {response.resource_names}")
except Exception as e:
    print(f"Error: {e}")
```

### Still Not Working?

1. **Check API Center**: 
   - Login to Google Ads account 310-946-3592
   - Go to Tools & Settings > API Center
   - Verify token status and usage

2. **Enable API Logging**:
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   logging.getLogger('google.ads.googleads.client').setLevel(logging.DEBUG)
   ```

3. **Contact Support**:
   - Post in [Google Ads API Forum](https://groups.google.com/g/google-ads-api)
   - Include request ID from error messages
   - Mention you have Basic Access approval

### Expected Success Response

When everything works correctly:
```json
{
  "accounts": [
    {
      "customer_id": "310-946-3592",
      "descriptive_name": "Brand Wisdom Solutions",
      "type": "MCC",
      "sub_accounts": [
        {
          "customer_id": "123-456-7890",
          "descriptive_name": "Client Account 1",
          "type": "STANDARD"
        }
      ]
    }
  ]
}
```