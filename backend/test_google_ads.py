"""
Test script to debug Google Ads API connection
"""
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings
from app.services.google_ads_real import google_ads_service
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_connection():
    """Test Google Ads API connection with a refresh token"""
    
    print("Google Ads API Test Script")
    print("=" * 50)
    
    # Check configuration
    print(f"Developer Token: {settings.GOOGLE_ADS_DEVELOPER_TOKEN[:10]}...")
    print(f"Login Customer ID: {settings.GOOGLE_ADS_LOGIN_CUSTOMER_ID}")
    print(f"Client ID: {settings.GOOGLE_ADS_CLIENT_ID[:20]}...")
    
    # You'll need to get a refresh token from the database or OAuth flow
    refresh_token = input("\nEnter refresh token (or 'skip' to exit): ").strip()
    
    if refresh_token.lower() == 'skip':
        return
    
    print("\nTesting API connection...")
    
    try:
        # Test 1: List accessible customers
        print("\n1. Testing list_accessible_customers...")
        customers = google_ads_service.list_accessible_customers(refresh_token)
        print(f"   Found {len(customers)} accessible customers:")
        for customer in customers:
            print(f"   - {customer}")
        
        # Test 2: Get account hierarchy
        print("\n2. Testing get_accounts_hierarchy...")
        accounts = google_ads_service.get_accounts_hierarchy(refresh_token)
        print(f"   Found {len(accounts)} accounts:")
        for account in accounts:
            print(f"   - {account['customer_id']}: {account['descriptive_name']} ({account['type']})")
            if account.get('sub_accounts'):
                print(f"     Sub-accounts: {len(account['sub_accounts'])}")
        
        print("\n✅ Success! Google Ads API is working correctly.")
        
    except Exception as e:
        print(f"\n❌ Error: {type(e).__name__}: {str(e)}")
        
        # Check for common issues
        if "PERMISSION_DENIED" in str(e):
            print("\n⚠️  Permission denied. Check:")
            print("   - OAuth user has access to MCC 310-946-3592")
            print("   - Developer token has proper access level")
            print("   - Customer ID format is correct (no dashes)")
        elif "UNAUTHENTICATED" in str(e):
            print("\n⚠️  Authentication failed. Check:")
            print("   - Refresh token is valid")
            print("   - OAuth credentials are correct")

if __name__ == "__main__":
    test_connection()