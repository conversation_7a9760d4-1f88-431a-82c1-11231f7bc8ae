"""
Mock Google Ads service for development
Returns realistic fake data without requiring Google Ads API setup
"""

from typing import List, Dict, Any
from datetime import datetime, timedelta
import random

class MockGoogleAdsService:
    """Mock service that returns realistic Google Ads data"""
    
    @staticmethod
    def get_mock_accounts() -> List[Dict[str, Any]]:
        """Return mock Google Ads accounts with manager account structure"""
        return [
            {
                "customer_id": "123-456-7890",
                "descriptive_name": "Master MCC Account - Brand Wisdom Agency",
                "type": "MCC",  # Manager Customer Center
                "currency_code": "USD",
                "time_zone": "America/New_York",
                "status": "ENABLED",
                "metrics": {
                    "spend": 45000,
                    "impressions": 2500000,
                    "clicks": 65000,
                    "ctr": 2.6,
                    "conversions": 850,
                    "active_campaigns": 24
                },
                "sub_accounts": [
                    {
                        "customer_id": "123-456-7891",
                        "descriptive_name": "Tech Startup - Search Campaigns",
                        "type": "STANDARD",
                        "spend": 12000,
                        "active_campaigns": 6
                    },
                    {
                        "customer_id": "123-456-7892",
                        "descriptive_name": "E-commerce Client - Shopping",
                        "type": "STANDARD",
                        "spend": 18000,
                        "active_campaigns": 8
                    },
                    {
                        "customer_id": "************",
                        "descriptive_name": "SaaS Company - Lead Gen",
                        "type": "STANDARD",
                        "spend": 15000,
                        "active_campaigns": 10
                    }
                ]
            },
            {
                "customer_id": "************",
                "descriptive_name": "Direct Client - Healthcare Services",
                "type": "STANDARD",
                "currency_code": "USD",
                "time_zone": "America/Los_Angeles",
                "status": "ENABLED",
                "metrics": {
                    "spend": 8500,
                    "impressions": 450000,
                    "clicks": 12000,
                    "ctr": 2.67,
                    "conversions": 180,
                    "active_campaigns": 4
                },
                "sub_accounts": []
            }
        ]
    
    @staticmethod
    def get_mock_campaigns(customer_id: str) -> List[Dict[str, Any]]:
        """Return mock campaigns for an account"""
        campaigns = [
            {
                "id": f"{customer_id}_camp_001",
                "name": "Brand Keywords - Search",
                "status": "ENABLED",
                "type": "SEARCH",
                "budget": 5000.00,
                "impressions": random.randint(100000, 150000),
                "clicks": random.randint(2500, 3500),
                "cost": random.uniform(3500, 4500),
                "conversions": random.uniform(40, 60),
                "ctr": random.uniform(2.0, 3.5),
                "average_cpc": random.uniform(1.0, 1.5)
            },
            {
                "id": f"{customer_id}_camp_002",
                "name": "Product Search - High Intent",
                "status": "ENABLED",
                "type": "SEARCH",
                "budget": 8000.00,
                "impressions": random.randint(80000, 120000),
                "clicks": random.randint(1800, 2500),
                "cost": random.uniform(6000, 7500),
                "conversions": random.uniform(25, 40),
                "ctr": random.uniform(1.8, 2.8),
                "average_cpc": random.uniform(2.5, 3.5)
            },
            {
                "id": f"{customer_id}_camp_003",
                "name": "Competitor Terms",
                "status": "ENABLED",
                "type": "SEARCH",
                "budget": 3000.00,
                "impressions": random.randint(50000, 70000),
                "clicks": random.randint(800, 1200),
                "cost": random.uniform(2000, 2800),
                "conversions": random.uniform(10, 20),
                "ctr": random.uniform(1.2, 2.0),
                "average_cpc": random.uniform(2.0, 3.0)
            }
        ]
        return campaigns
    
    @staticmethod
    def get_mock_search_terms() -> Dict[str, Any]:
        """Return mock search term analysis"""
        opportunities = [
            {
                "search_term": "ai google ads optimization",
                "impressions": 2450,
                "clicks": 145,
                "conversions": 12,
                "cost": 287.50,
                "conversion_rate": 8.28,
                "average_cpc": 1.98,
                "recommendation": "Add as exact match keyword",
                "potential_impact": "+$2,400/month revenue"
            },
            {
                "search_term": "automated ppc management",
                "impressions": 1820,
                "clicks": 98,
                "conversions": 8,
                "cost": 245.00,
                "conversion_rate": 8.16,
                "average_cpc": 2.50,
                "recommendation": "Add as phrase match keyword",
                "potential_impact": "+$1,600/month revenue"
            },
            {
                "search_term": "google ads ai tool",
                "impressions": 1350,
                "clicks": 67,
                "conversions": 5,
                "cost": 167.50,
                "conversion_rate": 7.46,
                "average_cpc": 2.50,
                "recommendation": "Add as broad match modifier",
                "potential_impact": "+$1,000/month revenue"
            }
        ]
        
        waste = [
            {
                "search_term": "free google ads",
                "impressions": 4500,
                "clicks": 450,
                "conversions": 0,
                "cost": 675.00,
                "conversion_rate": 0.0,
                "average_cpc": 1.50,
                "recommendation": "Add as negative keyword",
                "potential_savings": "$675/month"
            },
            {
                "search_term": "google ads tutorial",
                "impressions": 3200,
                "clicks": 280,
                "conversions": 0,
                "cost": 420.00,
                "conversion_rate": 0.0,
                "average_cpc": 1.50,
                "recommendation": "Add as negative keyword",
                "potential_savings": "$420/month"
            },
            {
                "search_term": "how to use google ads",
                "impressions": 2100,
                "clicks": 150,
                "conversions": 0,
                "cost": 300.00,
                "conversion_rate": 0.0,
                "average_cpc": 2.00,
                "recommendation": "Add as negative keyword",
                "potential_savings": "$300/month"
            }
        ]
        
        return {
            "analysis_date": datetime.utcnow().isoformat(),
            "total_search_terms_analyzed": 1247,
            "opportunities_found": len(opportunities),
            "waste_identified": len(waste),
            "potential_monthly_revenue": "$5,000",
            "potential_monthly_savings": "$1,395",
            "opportunities": opportunities,
            "waste": waste
        }