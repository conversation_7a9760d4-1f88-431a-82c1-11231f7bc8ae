# Add this endpoint to your main.py to save account data

@app.post("/api/google-ads/save-accounts")
async def save_google_ads_accounts(
    current_user: UserProfile = Depends(get_current_user)
):
    """Fetch and save Google Ads accounts to database for development"""
    try:
        # Get user's refresh token
        refresh_token = user_tokens.get(current_user.id)
        if not refresh_token:
            raise HTTPException(status_code=400, detail="Google Ads not connected")
        
        # Fetch accounts from Google Ads API
        accounts = RealGoogleAdsService.get_accounts_hierarchy(refresh_token)
        
        # Save to database
        saved_accounts = []
        headers = {
            "apikey": settings.SUPABASE_SERVICE_KEY,
            "Authorization": f"Bearer {settings.SUPABASE_SERVICE_KEY}",
            "Content-Type": "application/json"
        }
        
        async with httpx.AsyncClient() as client:
            for account in accounts:
                # Prepare account data
                account_data = {
                    "customer_id": account.get('customer_id'),
                    "name": account.get('descriptive_name'),
                    "currency_code": account.get('currency_code', 'USD'),
                    "time_zone": account.get('time_zone', 'America/New_York'),
                    "status": "ACTIVE",
                    "account_type": account.get('type', 'STANDARD'),
                    "sync_status": "synced",
                    "last_sync_at": datetime.utcnow().isoformat(),
                    "metadata": {
                        "test_account": account.get('test_account', False),
                        "sub_accounts": account.get('sub_accounts', [])
                    }
                }
                
                # Check if account exists
                check_response = await client.get(
                    f"{settings.SUPABASE_URL}/rest/v1/google_ads_accounts?customer_id=eq.{account_data['customer_id']}",
                    headers=headers
                )
                
                if check_response.status_code == 200 and check_response.json():
                    # Update existing
                    response = await client.patch(
                        f"{settings.SUPABASE_URL}/rest/v1/google_ads_accounts?customer_id=eq.{account_data['customer_id']}",
                        headers=headers,
                        json=account_data
                    )
                else:
                    # Insert new
                    response = await client.post(
                        f"{settings.SUPABASE_URL}/rest/v1/google_ads_accounts",
                        headers=headers,
                        json=account_data
                    )
                
                saved_accounts.append(account_data)
        
        return {
            "message": f"Saved {len(saved_accounts)} accounts to database",
            "accounts": saved_accounts
        }
        
    except Exception as e:
        logger.error(f"Error saving accounts: {e}")
        raise HTTPException(status_code=500, detail=str(e))