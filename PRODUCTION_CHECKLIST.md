# Production Readiness Checklist for Google Ads API Integration

## ✅ Current Status
- [x] Frontend UI/UX complete and polished
- [x] OAuth flow implemented in backend
- [x] Token storage in Supabase configured
- [x] Basic API integration working with test accounts
- [ ] Production credentials configured
- [ ] Security measures implemented
- [ ] Error handling enhanced
- [ ] Production deployment ready

## 🔐 1. Update OAuth Credentials (CRITICAL - Do This First!)

### Current Test Credentials (in .env):
```
GOOGLE_ADS_CLIENT_ID=*************-qj2cokqle91qjnb9ulba5n5fqild9m8f.apps.googleusercontent.com
GOOGLE_ADS_CLIENT_SECRET=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
```

### Actions Required:
1. Go to [Google Cloud Console](https://console.cloud.google.com)
2. Create/Update OAuth 2.0 credentials for production
3. Add production redirect URIs:
   - `https://yourdomain.com/api/google-ads/auth/callback`
   - Keep localhost for development
4. Update .env with production credentials

## 🔑 2. Update Google Ads API Configuration

### Current Configuration:
```
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jlhjqA
GOOGLE_ADS_LOGIN_CUSTOMER_ID=605-234-4141  # This is wrong - should be 310-946-3592
```

### Actions Required:
1. Update LOGIN_CUSTOMER_ID to your MCC: `310-946-3592`
2. Verify developer token has Basic Access (not just test access)
3. Remove any hardcoded test account IDs from the code

## 🛡️ 3. Implement Security Measures

### Add to backend/app/main.py:
```python
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

# Rate limiting
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter
app.add_exception_handler(429, _rate_limit_exceeded_handler)

# Security headers
@app.middleware("http")
async def add_security_headers(request: Request, call_next):
    response = await call_next(request)
    response.headers["X-Content-Type-Options"] = "nosniff"
    response.headers["X-Frame-Options"] = "DENY"
    response.headers["X-XSS-Protection"] = "1; mode=block"
    return response

# Add to sensitive endpoints
@app.get("/api/google-ads/auth/url")
@limiter.limit("5/minute")
async def get_google_ads_auth_url(...):
    ...
```

## 📊 4. Database Security

### Run these SQL commands in Supabase:
```sql
-- Enable RLS on token table
ALTER TABLE user_google_ads_tokens ENABLE ROW LEVEL SECURITY;

-- Create policy for token access
CREATE POLICY "Users can only access their own tokens" ON user_google_ads_tokens
  FOR ALL USING (auth.uid() = user_id);

-- Add encryption for sensitive fields (if using Supabase Vault)
-- UPDATE user_google_ads_tokens 
-- SET refresh_token = vault.encrypt(refresh_token, 'google-ads-key');
```

## 🔄 5. Enhanced Error Handling

### Update backend services with:
```python
class GoogleAdsAPIError(Exception):
    """Custom exception for Google Ads API errors"""
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

# In API calls:
try:
    # API call
except GoogleAdsException as ex:
    logger.error(f'Request failed with status {ex.error.code().name}')
    logger.error(f'Failure details: {ex.failure.errors}')
    
    # Don't expose internal errors to frontend
    if 'UNAUTHENTICATED' in str(ex.error.code()):
        raise HTTPException(status_code=401, detail="Authentication expired")
    elif 'QUOTA_EXCEEDED' in str(ex.error.code()):
        raise HTTPException(status_code=429, detail="API quota exceeded")
    else:
        raise HTTPException(status_code=500, detail="Google Ads API error")
```

## ✅ 6. Pre-Production Testing

### Test these scenarios:
1. **Fresh OAuth Flow**:
   - Clear all tokens
   - Complete OAuth from scratch
   - Verify token storage

2. **Token Refresh**:
   - Wait for access token to expire (1 hour)
   - Make API call
   - Verify automatic refresh

3. **Error Scenarios**:
   - Invalid credentials
   - Expired refresh token
   - API quota exceeded
   - Network failures

4. **Multi-Account Access**:
   - Connect with MCC account
   - Verify access to all sub-accounts
   - Test switching between accounts

## 🚀 7. Environment Variables for Production

### Create .env.production:
```bash
# API Settings
SECRET_KEY=<generate-new-secure-key>
ENVIRONMENT=production

# Supabase (use production project)
SUPABASE_URL=https://your-prod-project.supabase.co
SUPABASE_KEY=<production-anon-key>
SUPABASE_SERVICE_KEY=<production-service-key>

# Google Ads API
GOOGLE_ADS_DEVELOPER_TOKEN=<your-token>
GOOGLE_ADS_CLIENT_ID=<production-oauth-client-id>
GOOGLE_ADS_CLIENT_SECRET=<production-oauth-secret>
GOOGLE_ADS_LOGIN_CUSTOMER_ID=310-946-3592

# OAuth Configuration
GOOGLE_OAUTH_REDIRECT_URI=https://yourdomain.com/api/google-ads/auth/callback
FRONTEND_URL=https://yourdomain.com

# Remove or disable in production
# GOOGLE_ADS_USE_TEST_ACCOUNT=false
```

## 📝 8. Legal & Compliance

### Before going live:
1. **Terms of Service**: Include Google Ads API attribution
2. **Privacy Policy**: Explain data collection and usage
3. **User Consent**: Add consent screen before OAuth
4. **Data Retention**: Implement 90-day token refresh policy

## 🔧 9. Quick Backend Fixes Needed

### Fix 1: Update Login Customer ID
```python
# In backend/app/services/google_ads_real.py
# Change line ~92:
if login_customer_id:
    config_dict["login_customer_id"] = login_customer_id
# To use the correct MCC by default:
config_dict["login_customer_id"] = login_customer_id or "**********"  # Remove dashes
```

### Fix 2: Add Production Checks
```python
# In backend/app/main.py
# Add at startup:
@app.on_event("startup")
async def startup_event():
    if settings.ENVIRONMENT == "production":
        # Verify all required credentials
        required = [
            settings.GOOGLE_ADS_DEVELOPER_TOKEN,
            settings.GOOGLE_ADS_CLIENT_ID,
            settings.GOOGLE_ADS_CLIENT_SECRET,
            settings.SUPABASE_SERVICE_KEY
        ]
        if any(not val or val.startswith("sk-...") for val in required):
            raise ValueError("Missing or invalid production credentials!")
```

### Fix 3: Secure Token Storage
```python
# Add encryption helper
from cryptography.fernet import Fernet

def encrypt_token(token: str) -> str:
    # Use environment variable for encryption key
    key = settings.ENCRYPTION_KEY.encode()
    f = Fernet(key)
    return f.encrypt(token.encode()).decode()

def decrypt_token(encrypted_token: str) -> str:
    key = settings.ENCRYPTION_KEY.encode()
    f = Fernet(key)
    return f.decrypt(encrypted_token.encode()).decode()
```

## 🎯 Final Steps

1. **Update all credentials** in .env
2. **Test OAuth flow** with real account
3. **Verify API access** to your MCC
4. **Deploy backend** to Railway/Heroku
5. **Update frontend** API URLs
6. **Test end-to-end** in production

## ⚠️ Important Notes

- Never commit real credentials to git
- Use environment variables for all secrets
- Monitor API usage to avoid unexpected costs
- Keep test environment separate from production
- Regular security audits of token storage