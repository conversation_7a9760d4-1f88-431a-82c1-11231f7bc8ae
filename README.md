# AdsAI Platform - Google Ads Campaign Management

**Brand Wisdom Solutions - Internal Agency Tool**  
**MCC Account**: 310-946-3592  
**Website**: https://brandwisdom.in/

## 🚀 Quick Start

```bash
# Frontend (React + Vite)
cd frontend
npm install
npm run dev          # localhost:5173

# Backend (FastAPI)
cd backend
source venv/bin/activate
python3 run_server.py    # localhost:8000
```

## 📁 Project Structure

```
AdsAI/
├── frontend/         # React + Tailwind CSS
├── backend/          # FastAPI + Google Ads API
├── memory-bank/      # Project documentation & context
├── google-ads-docs/  # Comprehensive API documentation
└── GOOGLE_ADS_API_APPROVAL.md  # API credentials & status
```

## 🧠 Memory Bank

The memory bank contains all project context organized into focused files:

- `project-foundation.md` - Core business context and goals
- `technical-architecture.md` - Tech stack and infrastructure
- `features-overview.md` - All 12 platform features
- `current-status.md` - Active issues and progress
- `google-ads-api.md` - API integration details
- `development-history.md` - Project evolution timeline
- `deployment-strategy.md` - Production deployment plan
- `ui-ux-patterns.md` - Design system and patterns

## 🔑 Key Features

### Core Campaign Management
- Multi-Client Dashboard
- Campaign Creation (all types)
- Bid & Budget Management
- Performance Reporting
- Monitoring & Alerts
- Keyword Management

### AI-Powered Optimization
- Search Query Mining
- Intent Classification
- Ad Copy Laboratory
- Negative Keyword AI
- Bid Intelligence
- Natural Language Insights

## 🛠️ Tech Stack

- **Frontend**: React 19.1 + Vite + Tailwind CSS
- **Backend**: FastAPI + Google Ads API
- **Database**: Supabase PostgreSQL
- **Auth**: Supabase Auth
- **Deployment**: Vercel (frontend) + Railway (backend)

## 📊 Current Status

**Issue**: OAuth works but no Google Ads data showing  
**Next Steps**: 
1. Deploy to production domain
2. Submit for Google Cloud API verification
3. Debug MCC access permissions

## 📞 Contact

**Email**: <EMAIL>  
**Developer Token**: See GOOGLE_ADS_API_APPROVAL.md