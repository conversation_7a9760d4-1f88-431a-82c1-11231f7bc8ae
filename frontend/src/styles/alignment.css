/* Alignment and UX Improvements */

/* Button Alignment Fixes */
button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  vertical-align: middle;
}

/* Ensure icons in buttons are properly sized and aligned */
button svg {
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
}

/* Fix text alignment in all flex containers */
.flex {
  align-items: center;
}

/* Consistent spacing for form elements */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
}

/* Card header alignment */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Stats card alignment */
.stat-card {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.stat-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* Table alignment */
th, td {
  vertical-align: middle;
}

/* Icon containers */
.icon-container {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Dropdown alignment */
.dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  text-align: left;
}

/* Badge alignment */
.badge {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
  vertical-align: middle;
}

/* Navigation item alignment */
.nav-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Header alignment */
.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Action bar alignment */
.action-bar {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* List item alignment */
.list-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

/* Improve button focus states */
button:focus-visible {
  outline: 2px solid #4172F5;
  outline-offset: 2px;
}

/* Consistent hover states */
button:not(:disabled):hover {
  transform: translateY(-1px);
}

/* Disabled state improvements */
button:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Loading state alignment */
.loading-spinner {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Empty state alignment */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 1rem;
  padding: 3rem 1.5rem;
}

/* Modal alignment */
.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* Tooltip alignment */
.tooltip {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

/* Improve text readability */
p, .text-content {
  line-height: 1.6;
}

/* Heading alignment */
h1, h2, h3, h4, h5, h6 {
  line-height: 1.3;
  margin-top: 0;
}

/* Link alignment with icons */
a {
  display: inline-flex;
  align-items: center;
  gap: 0.25rem;
}

/* Checkbox and radio alignment */
input[type="checkbox"],
input[type="radio"] {
  margin-right: 0.5rem;
  vertical-align: middle;
}

/* Form label with required indicator */
.required-field::after {
  content: " *";
  color: #EF4444;
}

/* Improve spacing consistency */
.space-y-2 > * + * {
  margin-top: 0.5rem;
}

.space-y-4 > * + * {
  margin-top: 1rem;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

/* Responsive text alignment */
@media (max-width: 640px) {
  .sm\:text-center {
    text-align: center;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
  }
  
  .action-bar {
    justify-content: stretch;
  }
  
  .action-bar > * {
    flex: 1;
  }
}