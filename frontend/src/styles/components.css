/* Enhanced Component Styles */

/* Modern Card Component */
.dashboard-card {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  padding: var(--space-5);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all var(--transition-base) cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.dashboard-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, 
    var(--brand-primary-500) 0%, 
    var(--brand-primary-600) 50%, 
    var(--brand-primary-500) 100%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  opacity: 0;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-hover);
  border-color: rgba(65, 114, 245, 0.1);
}

.dashboard-card:hover::before {
  transform: translateX(0);
  opacity: 1;
}

/* Card Header */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.card-title {
  font-family: var(--font-heading);
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--brand-primary-900);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.card-icon {
  width: 20px;
  height: 20px;
  color: var(--brand-primary-600);
}

/* Stat Cards */
.stat-card {
  background: var(--brand-white);
  border-radius: var(--radius-md);
  padding: var(--space-4);
  box-shadow: var(--shadow-xs);
  border: 1px solid rgba(0, 0, 0, 0.04);
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.stat-value {
  font-size: 2rem;
  font-weight: 800;
  color: var(--brand-primary-900);
  font-family: var(--font-heading);
  line-height: 1.2;
  margin-bottom: var(--space-1);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--brand-gray-400);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.stat-trend {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  margin-top: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
}

.stat-trend.positive {
  background: rgba(39, 192, 132, 0.1);
  color: var(--brand-success);
}

.stat-trend.negative {
  background: rgba(239, 94, 94, 0.1);
  color: var(--brand-error);
}

/* Enhanced Buttons */
.btn-primary {
  background: var(--brand-primary-600);
  color: var(--brand-white);
  padding: 0.625rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 2.5rem;
  white-space: nowrap;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-full);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn-primary:hover {
  background: var(--brand-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-primary:hover::before {
  width: 300px;
  height: 300px;
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

/* Secondary Button */
.btn-secondary {
  background: transparent;
  color: var(--brand-primary-600);
  padding: 0.625rem 1rem;
  border: 1px solid var(--brand-primary-600);
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 2.5rem;
  white-space: nowrap;
}

.btn-secondary:hover {
  background: var(--brand-primary-50);
  border-color: var(--brand-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

/* Ghost Button */
.btn-ghost {
  background: transparent;
  color: var(--brand-gray-600);
  padding: 0.625rem 1rem;
  border: none;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  cursor: pointer;
  transition: all var(--transition-base);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 2.5rem;
  white-space: nowrap;
}

.btn-ghost:hover {
  background: var(--brand-gray-100);
  color: var(--brand-primary-600);
}

/* Button Danger Variant */
.btn-danger {
  background: var(--brand-error);
  color: var(--brand-white);
  padding: 0.625rem 1rem;
  border-radius: var(--radius-sm);
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 2.5rem;
  white-space: nowrap;
  box-shadow: var(--shadow-sm);
}

.btn-danger:hover {
  background: #DC5151;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* Button Link Variant */
.btn-link {
  background: transparent;
  color: var(--brand-primary-600);
  padding: 0;
  border: none;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all var(--transition-base);
  text-decoration: underline;
  text-underline-offset: 4px;
}

.btn-link:hover {
  color: var(--brand-primary-700);
}

/* Button Sizes */
.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  min-height: 2rem;
}

.btn-lg {
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  min-height: 2.75rem;
}

.btn-icon {
  width: 2.25rem;
  height: 2.25rem;
  padding: 0;
  min-height: unset;
}

/* Button Loading State */
.btn-loading {
  color: transparent;
  pointer-events: none;
}

.btn-loading .btn-spinner {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 1rem;
  height: 1rem;
  color: currentColor;
  animation: spin 1s linear infinite;
}

/* Utility Classes */
.w-full {
  width: 100%;
}

@keyframes spin {
  from {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  to {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* Form Elements */
.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--brand-gray-700);
  margin-bottom: var(--space-2);
  line-height: 1.5;
}

.form-label.required::after {
  content: '*';
  color: var(--brand-error);
  margin-left: var(--space-1);
}

.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: var(--brand-white);
  border: 1px solid #E5E7EB;
  border-radius: var(--radius-sm);
  font-family: var(--font-primary);
  font-size: 0.875rem;
  color: var(--brand-gray-900);
  transition: all var(--transition-fast);
  position: relative;
}

.form-input:focus {
  outline: none;
  border-color: var(--brand-primary-500);
  box-shadow: 0 0 0 3px rgba(65, 114, 245, 0.1);
}

.form-input::placeholder {
  color: var(--brand-gray-400);
}

/* Select Dropdown */
.form-select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
}

/* Campaign Overview */
.campaign-overview-content {
  padding: var(--space-4);
}

.campaign-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.campaign-item {
  border-bottom: 1px solid var(--brand-gray-100);
  padding-bottom: var(--space-4);
}

.campaign-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.campaign-item-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.campaign-item-info {
  flex: 1;
}

.campaign-name {
  font-weight: 500;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-1);
}

.campaign-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.campaign-budget {
  font-size: 0.75rem;
  color: var(--brand-gray-500);
}

.quality-score {
  text-align: center;
}

.quality-score-label {
  font-size: 0.75rem;
  color: var(--brand-gray-500);
  margin-bottom: var(--space-1);
}

.quality-score-value {
  font-size: 1.25rem;
  font-weight: 600;
}

.quality-score-value.excellent {
  color: var(--brand-success);
}

.quality-score-value.good {
  color: var(--brand-warning);
}

.quality-score-value.poor {
  color: var(--brand-error);
}

.campaign-metrics {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: var(--space-4);
  margin-bottom: var(--space-4);
}

.campaign-metric {
  text-align: left;
}

.campaign-metric-label {
  font-size: 0.75rem;
  color: var(--brand-gray-500);
}

.campaign-metric-value {
  font-weight: 500;
  font-size: 0.875rem;
  color: var(--brand-gray-900);
  margin-top: 0.125rem;
}

.campaign-metric-value.metric-positive {
  color: var(--brand-success);
}

.campaign-metric-value.metric-negative {
  color: var(--brand-error);
}

.campaign-actions {
  display: flex;
  gap: var(--space-2);
}

.campaign-actions button {
  flex: 1;
}

.status-icon {
  width: 0.75rem;
  height: 0.75rem;
}

.performance-trend {
  margin-bottom: 0.75rem;
  padding: 0.75rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
}

/* Loading States */
.skeleton-box {
  background: linear-gradient(90deg, 
    var(--brand-gray-100) 25%, 
    rgba(65, 114, 245, 0.05) 50%, 
    var(--brand-gray-100) 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
}

.skeleton-text {
  height: 1em;
  margin-bottom: var(--space-2);
}

.skeleton-title {
  height: 1.5em;
  width: 60%;
  margin-bottom: var(--space-3);
}

.skeleton-chart {
  background: var(--brand-gray-100);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.skeleton-chart-bar {
  flex: 1;
  background: linear-gradient(180deg, 
    rgba(65, 114, 245, 0.1) 0%, 
    rgba(65, 114, 245, 0.05) 100%);
  border-radius: var(--radius-sm) var(--radius-sm) 0 0;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.5;
  }
  50% {
    opacity: 1;
  }
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-3);
  border-radius: var(--radius-full);
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-badge.active {
  background: rgba(39, 192, 132, 0.1);
  color: var(--brand-success);
}

.status-badge.inactive {
  background: rgba(107, 114, 128, 0.1);
  color: var(--brand-gray-400);
}

.status-badge.warning {
  background: rgba(254, 205, 121, 0.1);
  color: var(--brand-warning);
}

.status-badge.error {
  background: rgba(239, 94, 94, 0.1);
  color: var(--brand-error);
}

/* Pulse Indicator */
.pulse-dot {
  display: inline-block;
  width: 6px;
  height: 6px;
  background: currentColor;
  border-radius: var(--radius-full);
  position: relative;
}

.pulse-dot::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: currentColor;
  border-radius: inherit;
  animation: pulse 2s ease-in-out infinite;
}

/* Table Styles */
.data-table {
  width: 100%;
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.data-table thead {
  background: var(--brand-gray-100);
}

.data-table th {
  padding: var(--space-3) var(--space-4);
  text-align: left;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--brand-gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.data-table td {
  padding: var(--space-4);
  border-top: 1px solid var(--brand-gray-100);
  font-size: 0.875rem;
  color: var(--brand-gray-900);
}

.data-table tr {
  transition: all var(--transition-fast);
}

.data-table tbody tr:hover {
  background: var(--brand-gray-100);
}

.table-header {
  border-bottom: 1px solid var(--brand-gray-100);
  padding: 1rem;
  background: var(--bg-secondary);
}

.table-body {
  position: relative;
}

.table-body > div {
  border-bottom: 1px solid var(--brand-gray-100);
  padding: 1rem;
}

.table-body > div:last-child {
  border-bottom: none;
}

/* Quick Action Cards */
.quick-action {
  background: var(--brand-white);
  border: 2px solid transparent;
  border-radius: var(--radius-md);
  padding: var(--space-4);
  text-align: center;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
}

.quick-action:hover {
  border-color: var(--brand-primary-500);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.quick-action-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto var(--space-3);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md);
  background: var(--brand-primary-50);
  color: var(--brand-primary-600);
  transition: all var(--transition-base);
}

.quick-action:hover .quick-action-icon {
  transform: scale(1.1);
  background: var(--brand-primary-100);
}

.quick-action-title {
  font-weight: 600;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-1);
}

.quick-action-desc {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
}

/* Notification Bell Animation */
.notification-bell {
  position: relative;
}

.notification-bell.has-notifications::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(--brand-error);
  border-radius: var(--radius-full);
  border: 2px solid var(--brand-white);
  animation: bellPulse 2s ease-in-out infinite;
}

@keyframes bellPulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

/* Activity Feed */
.recent-activity-content {
  padding: var(--space-4);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
}

.activity-item {
  display: flex;
  gap: var(--space-3);
  align-items: flex-start;
}

.activity-item:hover {
  transform: translateX(2px);
}

.activity-icon-wrapper {
  padding: 0.375rem;
  border-radius: var(--radius-md);
  flex-shrink: 0;
}

.activity-icon-wrapper.optimization {
  background: rgba(39, 192, 132, 0.1);
}

.activity-icon-wrapper.alert {
  background: rgba(254, 205, 121, 0.1);
}

.activity-icon-wrapper.success {
  background: rgba(65, 114, 245, 0.1);
}

.activity-icon-wrapper.info {
  background: var(--brand-gray-100);
}

.activity-icon {
  width: 1rem;
  height: 1rem;
}

.activity-icon.optimization {
  color: var(--brand-success);
}

.activity-icon.alert {
  color: var(--brand-warning);
}

.activity-icon.success {
  color: var(--brand-primary-600);
}

.activity-icon.info {
  color: var(--brand-gray-500);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-message {
  font-size: 0.875rem;
  color: var(--brand-gray-900);
  line-height: 1.4;
}

.activity-impact {
  font-size: 0.75rem;
  color: var(--brand-gray-500);
  margin-top: 0.125rem;
}

.activity-time {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-top: var(--space-1);
}

.activity-time-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Account Manager */
.account-card {
  overflow: hidden;
  border: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
  transition: all 0.2s ease-in-out;
}

.account-card:hover {
  box-shadow: var(--shadow-md);
}

.account-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--brand-gray-900);
}

.account-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.25rem;
}

.metric-card {
  background: var(--brand-white);
  border-radius: 0.5rem;
  padding: 1rem;
  text-align: center;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease-in-out;
}

.metric-card:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.metric-label {
  font-size: 0.875rem;
  color: var(--brand-gray-600);
  font-weight: 500;
  margin-bottom: var(--space-2);
}

.metric-value {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--brand-gray-900);
  font-family: var(--font-heading);
}

/* Account Content Expanded */
.account-content-expanded {
  border-top: 1px solid var(--border-color);
  background: var(--brand-gray-100);
  padding: var(--space-5);
}

.sub-account-card {
  padding: 1.25rem;
  background: var(--brand-white);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
  transition: all 0.2s ease-in-out;
}

.sub-account-card:hover {
  box-shadow: var(--shadow-sm);
}

/* Sub Accounts Section */
.sub-accounts-section {
  margin-bottom: var(--space-6);
}

.sub-accounts-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-4);
}

.sub-accounts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--space-4);
}

.sub-account-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.sub-account-info {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.sub-account-icon {
  padding: var(--space-2);
  background: var(--brand-gray-100);
  border-radius: var(--radius-md);
  color: var(--brand-primary-600);
}

.sub-account-icon svg {
  width: 1rem;
  height: 1rem;
}

.sub-account-name {
  font-weight: 600;
  font-size: 1rem;
  color: var(--brand-gray-900);
}

.sub-account-meta {
  font-size: 0.875rem;
  color: var(--brand-gray-600);
}

.sub-account-spend {
  text-align: right;
}

.sub-account-spend-value {
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--brand-gray-900);
}

.sub-account-spend-label {
  font-size: 0.875rem;
  color: var(--brand-gray-500);
}

.sub-account-note {
  font-size: 0.875rem;
  color: var(--brand-warning);
  background: rgba(254, 205, 121, 0.1);
  padding: var(--space-3) var(--space-3);
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-4);
}

/* Account Actions */
.account-actions {
  display: flex;
  gap: var(--space-3);
  padding-top: var(--space-5);
  border-top: 1px solid var(--border-color);
}

.account-actions .btn-secondary {
  flex: 1;
}

/* Accounts Container */
.accounts-container {
  display: flex;
  flex-direction: column;
  gap: var(--space-6);
}

.accounts-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

/* Account Header Styles */
.account-header {
  cursor: pointer;
  padding: var(--space-5);
  transition: background-color var(--transition-base);
}

.account-header:hover {
  background-color: var(--brand-gray-100);
}

.account-header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.account-header-left {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.account-header-right {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.chevron-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--brand-gray-600);
  transition: transform var(--transition-base);
}

.account-icon-wrapper {
  padding: 0.625rem;
  background: var(--brand-gray-100);
  border-radius: var(--radius-md);
}

.account-type-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--brand-primary-600);
}

.account-type-icon.success {
  color: var(--brand-success);
}

.account-meta {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  margin-top: var(--space-1);
}

.account-id {
  font-size: 0.875rem;
  color: var(--brand-gray-600);
}

.account-metric {
  text-align: right;
}

.account-metric-label {
  font-size: 0.875rem;
  color: var(--brand-gray-600);
  font-weight: 500;
}

.account-metric-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-gray-900);
  font-family: var(--font-heading);
}

/* Badge Variants */
.badge-primary {
  background: var(--brand-primary-600);
  color: var(--brand-white);
}

.badge-success {
  background: rgba(39, 192, 132, 0.1);
  color: var(--brand-success);
}

.badge-warning {
  background: rgba(254, 205, 121, 0.1);
  color: var(--brand-warning);
}

/* Error States */
.error-card {
  border-color: rgba(239, 94, 94, 0.2);
  background-color: rgba(239, 94, 94, 0.05);
}

/* Chart Styles */
.chart-tooltip {
  background: var(--brand-white);
  padding: 1rem;
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-color);
}

.tooltip-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--brand-gray-400);
  margin-bottom: 0.5rem;
  font-family: var(--font-primary);
}

.tooltip-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-4);
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
}

.tooltip-label {
  color: var(--brand-gray-600);
}

.tooltip-value {
  font-weight: 500;
}

/* Chart Container */
.chart-container {
  padding: var(--space-4);
}

.chart-wrapper {
  height: 16rem;
  width: 100%;
}

.chart-wrapper.lg {
  height: 20rem;
}

/* Sparkline */
.sparkline-container {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  height: 100%;
}

.sparkline-chart {
  flex: 1;
  height: 100%;
}

.sparkline-trend {
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
}

.sparkline-trend.positive {
  color: var(--brand-success);
}

.sparkline-trend.negative {
  color: var(--brand-error);
}

/* Alerts */
.alert {
  position: relative;
  width: 100%;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  padding: var(--space-4);
  background: var(--brand-gray-100);
  color: var(--brand-primary-900);
}

.alert-error {
  background: rgba(239, 68, 68, 0.05);
  border-color: var(--brand-error);
  color: var(--brand-error);
}

.alert-success {
  background: rgba(16, 185, 129, 0.05);
  border-color: var(--brand-success);
  color: var(--brand-success);
}

.alert-warning {
  background: rgba(254, 205, 121, 0.1);
  border-color: var(--brand-gold-500);
  color: var(--brand-primary-900);
}

.alert-title {
  margin-bottom: 0.25rem;
  font-weight: 500;
  line-height: 1.25;
  letter-spacing: -0.025em;
}

.alert-description {
  font-size: 0.875rem;
  line-height: 1.6;
}

.alert-description p {
  line-height: 1.6;
}

/* Test Account Banner */
.test-account-banner {
  margin-bottom: var(--space-6);
}

.test-account-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
}

.test-account-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--brand-gold-700);
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.test-account-body {
  flex: 1;
}

.test-account-title {
  font-weight: 600;
  color: var(--brand-gold-700);
  margin-bottom: var(--space-2);
}

.test-account-description {
  font-size: 0.875rem;
  color: var(--brand-gold-600);
  margin-bottom: var(--space-3);
}

.test-account-list {
  font-size: 0.875rem;
  color: var(--brand-gold-600);
  margin-bottom: var(--space-3);
  list-style: none;
}

.test-account-list li {
  margin-bottom: 0.25rem;
}

.test-account-actions {
  display: flex;
  align-items: center;
  gap: var(--space-4);
  padding-top: var(--space-2);
}

.test-account-link {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--brand-primary-600);
  text-decoration: none;
  transition: color var(--transition-base);
}

.test-account-link:hover {
  color: var(--brand-primary-700);
}

.test-account-link-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Info Box */
.info-box {
  background: var(--brand-gray-100);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  padding: var(--space-5);
  font-size: 0.875rem;
  color: var(--brand-gray-600);
}

.info-box-title {
  font-weight: 600;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-3);
}

.info-list {
  list-style: decimal;
  list-style-position: inside;
  space-y: var(--space-2);
}

.info-list li {
  margin-bottom: var(--space-2);
  line-height: 1.6;
}

/* Command Palette */
.command-palette-overlay {
  position: fixed;
  inset: 0;
  z-index: 50;
  overflow-y: auto;
}

.command-palette-backdrop {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.command-palette-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 5rem;
}

.command-palette-card {
  position: relative;
  width: 100%;
  max-width: 42rem;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
}

.command-palette-search {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-4);
  border-bottom: 1px solid var(--brand-gray-100);
}

.command-palette-search-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--brand-gray-400);
}

.command-palette-input {
  flex: 1;
  font-size: 1rem;
  color: var(--brand-primary-900);
  background: transparent;
  outline: none;
  border: none;
}

.command-palette-input::placeholder {
  color: var(--brand-gray-400);
}

.command-palette-kbd {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--brand-gray-500);
  background: var(--brand-gray-100);
  border-radius: var(--radius-sm);
}

.command-palette-list {
  max-height: 24rem;
  overflow-y: auto;
}

.command-palette-empty {
  padding: var(--space-8);
  text-align: center;
}

.command-palette-empty-text {
  font-size: 1rem;
  color: var(--brand-text);
}

.command-palette-category {
  padding: var(--space-2) var(--space-4);
  background: rgba(var(--brand-gray-100-rgb), 0.5);
}

.command-palette-category-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--brand-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.command-palette-group {
  padding: var(--space-2) 0;
}

.command-palette-item {
  width: 100%;
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  text-align: left;
  transition: all var(--transition-base);
  background: transparent;
  border: none;
  cursor: pointer;
}

.command-palette-item:hover {
  background: rgba(var(--brand-gray-100-rgb), 0.5);
}

.command-palette-item.selected {
  background: var(--brand-primary-50);
  color: var(--brand-primary-900);
}

.command-palette-item-icon {
  padding: var(--space-2);
  border-radius: var(--radius-md);
  background: var(--brand-gray-100);
  transition: all var(--transition-base);
}

.command-palette-item-icon.selected {
  background: var(--brand-primary-100);
}

.command-icon {
  width: 1rem;
  height: 1rem;
  color: var(--brand-gray-500);
}

.command-palette-item.selected .command-icon {
  color: var(--brand-primary-600);
}

.command-palette-item-label {
  flex: 1;
  font-size: 1rem;
  font-weight: 500;
}

.command-palette-shortcut {
  padding: 0.125rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  border-radius: var(--radius-sm);
  background: var(--brand-gray-100);
  color: var(--brand-gray-500);
}

.command-palette-shortcut.selected {
  background: var(--brand-primary-100);
  color: var(--brand-primary-700);
}

.command-palette-footer {
  padding: var(--space-3);
  border-top: 1px solid var(--brand-gray-100);
  background: var(--brand-gray-50);
}

.command-palette-footer-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--brand-gray-500);
}

.command-palette-footer-left {
  display: flex;
  align-items: center;
  gap: var(--space-4);
}

.command-palette-footer-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.command-palette-footer-kbd {
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
  background: var(--brand-white);
  border: 1px solid var(--brand-gray-200);
  border-radius: var(--radius-xs);
}

.command-palette-footer-icon {
  width: 0.75rem;
  height: 0.75rem;
}

/* Loading States */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) 0;
  gap: var(--space-3);
  color: var(--brand-text);
}

.loading-spinner {
  animation: spin 0.8s ease-in-out infinite;
  color: var(--brand-primary-600);
}

.spinner-sm {
  width: 1rem;
  height: 1rem;
}

.spinner-md {
  width: 2rem;
  height: 2rem;
}

.spinner-lg {
  width: 3rem;
  height: 3rem;
}

.loading-spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
}

.loading-spinner-text {
  margin-top: var(--space-4);
  font-size: 0.875rem;
  color: var(--brand-gray-600);
}

.full-page-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--brand-gray-50);
}

.full-page-loader-content {
  text-align: center;
}

.full-page-loader-text {
  margin-top: var(--space-4);
  font-size: 1.125rem;
  color: var(--brand-gray-600);
}

.route-loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: calc(100vh - 4rem);
}

/* Error States */
.error-message {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  color: var(--brand-error);
  font-weight: 500;
  padding: var(--space-4);
}

.error-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* Modals */
.modal-overlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.modal-content {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  max-width: 28rem;
  width: 100%;
  padding: var(--space-6);
  box-shadow: var(--shadow-2xl);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  font-family: var(--font-heading);
  color: var(--brand-primary-900);
}

.modal-alert {
  margin-bottom: var(--space-4);
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
}

.alert-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.alert-icon.warning {
  color: var(--brand-gold-700);
}

.alert-icon.success {
  color: var(--brand-success);
}

.alert-icon.error {
  color: var(--brand-error);
}

.alert-body {
  flex: 1;
}

.alert-details {
  margin-top: var(--space-2);
  font-size: 0.875rem;
}

.alert-note {
  color: var(--brand-text);
}

.alert-solution {
  margin-top: 0.25rem;
  color: var(--brand-primary-600);
}

.modal-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.modal-footer {
  display: flex;
  gap: var(--space-3);
  padding-top: var(--space-2);
}

.modal-footer .btn-primary,
.modal-footer .btn-secondary {
  flex: 1;
}

/* Error Pages */
.error-page {
  min-height: 100vh;
  background: var(--brand-gray-50);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-4);
}

.error-container {
  max-width: 28rem;
  width: 100%;
  text-align: center;
}

.error-icon-wrapper {
  margin-bottom: var(--space-6);
}

.error-page-icon {
  width: 4rem;
  height: 4rem;
  color: var(--brand-error);
  margin: 0 auto;
}

.error-page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-2);
}

.error-page-message {
  color: var(--brand-gray-600);
  margin-bottom: var(--space-6);
}

.error-details {
  margin-bottom: var(--space-6);
  text-align: left;
}

.error-details-summary {
  cursor: pointer;
  font-size: 0.875rem;
  color: var(--brand-gray-500);
  transition: color var(--transition-base);
}

.error-details-summary:hover {
  color: var(--brand-gray-700);
}

.error-details-content {
  margin-top: var(--space-2);
  padding: var(--space-4);
  background: var(--brand-gray-100);
  border-radius: var(--radius-md);
  font-size: 0.75rem;
  overflow: auto;
  font-family: monospace;
}

.error-page-button {
  display: inline-flex;
  align-items: center;
  gap: var(--space-2);
}

.button-icon {
  width: 1rem;
  height: 1rem;
}

/* Error Fallback Component */
.error-fallback-card {
  max-width: 28rem;
  width: 100%;
}

.error-fallback-header {
  text-align: center;
}

.error-fallback-icon {
  width: 3rem;
  height: 3rem;
  color: var(--brand-error);
  margin: 0 auto var(--space-4);
}

.error-fallback-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-gray-900);
}

.error-fallback-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.error-fallback-message {
  color: var(--brand-gray-600);
  text-align: center;
}

.error-message-box {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
  border-radius: var(--radius-md);
  padding: var(--space-4);
}

.error-message-text {
  font-size: 0.875rem;
  color: var(--brand-error);
  font-family: monospace;
}

.error-fallback-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  padding-top: var(--space-4);
}

/* Onboarding */
.onboarding-page {
  min-height: 100vh;
  background: linear-gradient(to bottom right, var(--brand-primary-50), var(--brand-white));
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-6);
}

.onboarding-container {
  max-width: 64rem;
  width: 100%;
}

.onboarding-progress {
  margin-bottom: var(--space-8);
}

.onboarding-progress-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.onboarding-step-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--brand-text);
}

.onboarding-skip-button {
  font-size: 0.875rem;
  color: var(--brand-primary-600);
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition-base);
}

.onboarding-skip-button:hover {
  color: var(--brand-primary-700);
}

.onboarding-progress-bar {
  width: 100%;
  background: var(--brand-gray-100);
  border-radius: 9999px;
  height: 0.5rem;
  overflow: hidden;
}

.onboarding-progress-fill {
  background: var(--brand-primary-600);
  height: 100%;
  border-radius: 9999px;
  transition: width 300ms ease;
}

.onboarding-card {
  box-shadow: var(--shadow-lg);
}

.onboarding-card-header {
  text-align: center;
  padding-bottom: var(--space-2);
}

.onboarding-card-title {
  font-size: 1.875rem;
  font-family: var(--font-heading);
}

.onboarding-card-content {
  padding: var(--space-8);
}

.onboarding-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.onboarding-welcome {
  text-align: center;
  margin-bottom: var(--space-6);
}

.onboarding-icon-large {
  width: 6rem;
  height: 6rem;
  color: var(--brand-primary-600);
  margin: 0 auto var(--space-4);
}

.onboarding-title {
  font-size: 2.25rem;
  font-family: var(--font-heading);
  font-weight: 700;
  color: var(--brand-primary-900);
  margin-bottom: var(--space-2);
}

.onboarding-subtitle {
  font-size: 1.125rem;
  color: var(--brand-text);
}

.onboarding-highlight-box {
  background: var(--brand-primary-50);
  border-radius: var(--radius-lg);
  padding: var(--space-6);
}

.onboarding-box-title {
  font-weight: 600;
  color: var(--brand-primary-900);
  margin-bottom: var(--space-3);
}

.onboarding-checklist {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
  list-style: none;
}

.onboarding-checklist-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
}

.onboarding-check-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--brand-success);
  margin-top: 0.125rem;
  flex-shrink: 0;
}

.onboarding-info-card {
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  border: 1px solid var(--brand-gray-100);
}

.onboarding-card-title {
  font-weight: 600;
  color: var(--brand-primary-900);
  margin-bottom: var(--space-2);
}

.onboarding-hierarchy {
  display: flex;
  flex-direction: column;
  gap: var(--space-3);
  margin-left: var(--space-4);
}

.onboarding-hierarchy-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.hierarchy-indicator {
  width: 0.75rem;
  height: 0.75rem;
  background: var(--brand-success);
  border-radius: 50%;
  flex-shrink: 0;
}

.hierarchy-indicator.primary {
  width: 1rem;
  height: 1rem;
  background: var(--brand-primary-600);
  border-radius: var(--radius-sm);
}

.hierarchy-text {
  font-size: 1rem;
}

.hierarchy-text.small {
  font-size: 0.875rem;
  color: var(--brand-text);
}

.onboarding-hierarchy-children {
  margin-left: var(--space-6);
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.onboarding-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-6);
  border-top: 1px solid var(--brand-gray-100);
}

.onboarding-dots {
  display: flex;
  gap: var(--space-2);
}

.onboarding-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
  background: var(--brand-gray-100);
  transition: all var(--transition-base);
}

.onboarding-dot.active {
  background: var(--brand-primary-600);
  width: 2rem;
}

.onboarding-dot.completed {
  background: var(--brand-success);
}

.button-icon-right {
  width: 1rem;
  height: 1rem;
  margin-left: var(--space-2);
}

/* Empty States */
.empty-state {
  text-align: center;
  padding: var(--space-8) var(--space-4);
}

.empty-state-icon {
  width: 3rem;
  height: 3rem;
  color: var(--brand-gray-400);
  margin: 0 auto var(--space-4);
}

.empty-state-icon-wrapper {
  width: 5rem;
  height: 5rem;
  background: var(--brand-gray-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--space-4);
}

.empty-state-icon-wrapper .empty-state-icon {
  width: 2.5rem;
  height: 2.5rem;
  margin: 0;
}

.empty-state-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--brand-gray-900);
  margin-bottom: var(--space-2);
}

.empty-state-description {
  font-size: 0.875rem;
  color: var(--brand-gray-600);
  max-width: 24rem;
  margin: 0 auto;
}

/* Small empty state variants */
.empty-state-icon-wrapper.sm {
  width: 3rem;
  height: 3rem;
  margin-bottom: var(--space-3);
}

.empty-state-icon.sm {
  width: 1.5rem;
  height: 1.5rem;
}

.empty-state-title.sm {
  font-size: 0.875rem;
  margin-bottom: var(--space-1);
}

/* Auth Components */
.auth-card {
  width: 100%;
  max-width: 28rem;
  margin: 0 auto;
  background: var(--brand-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.auth-header {
  text-align: center;
  padding-bottom: var(--space-6);
}

.auth-title {
  font-family: var(--font-heading);
  font-size: 2rem;
  font-weight: 700;
  color: var(--brand-primary-900);
  margin-bottom: var(--space-2);
}

.auth-description {
  font-size: 1rem;
  color: var(--brand-text);
  margin-top: var(--space-2);
}

.auth-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-4);
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.input-wrapper {
  position: relative;
}

.input-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  width: 1rem;
  height: 1rem;
  color: var(--brand-gray-400);
}

.form-input.with-icon {
  padding-left: 2.5rem;
}

.auth-footer {
  margin-top: var(--space-6);
  text-align: center;
  font-size: 0.875rem;
}

.auth-footer-text {
  color: var(--brand-text);
}

.auth-success {
  text-align: center;
}

/* Utility Classes */
.w-full {
  width: 100%;
}