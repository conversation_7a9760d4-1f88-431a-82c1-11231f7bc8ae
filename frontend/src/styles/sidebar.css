/* Enhanced Sidebar Styles with Modern UI Patterns */

/* Sidebar Container */
.modern-sidebar {
  background: white;
  transition: all 200ms ease;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

/* Logo Section */
.sidebar-logo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  transition: all var(--transition-base);
  flex-shrink: 0;
}

.sidebar-logo-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  overflow: hidden;
  flex-shrink: 0;
  transition: transform var(--transition-base);
}

.sidebar-logo:hover .sidebar-logo-icon {
  transform: rotate(-5deg) scale(1.05);
}

.sidebar-logo-text h1 {
  font-family: var(--font-heading);
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--brand-primary-900);
  line-height: 1.2;
}

.sidebar-logo-text p {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
  font-weight: 500;
}

/* Navigation Items */
.sidebar-nav-item {
  position: relative;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  margin: 2px 8px;
  border-radius: var(--radius-sm);
  color: var(--brand-gray-400);
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all var(--transition-base);
  cursor: pointer;
}

.sidebar-nav-item:hover {
  background: rgba(65, 114, 245, 0.05);
  color: var(--brand-primary-600);
  transform: translateX(4px);
}

.sidebar-nav-item.active {
  background: rgba(65, 114, 245, 0.1);
  color: var(--brand-primary-600);
  font-weight: 600;
}

.sidebar-nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 3px;
  height: 20px;
  background: var(--brand-primary-600);
  border-radius: 0 3px 3px 0;
}

/* Navigation Icons */
.sidebar-nav-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  transition: all var(--transition-base);
}

.sidebar-nav-item:hover .sidebar-nav-icon {
  transform: scale(1.1);
}

/* AI Badge */
.sidebar-ai-badge {
  display: inline-flex;
  align-items: center;
  gap: 2px;
  background: rgba(65, 114, 245, 0.1);
  color: var(--brand-primary-600);
  padding: 2px 8px;
  border-radius: var(--radius-full);
  font-size: 0.625rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-left: auto;
}

/* Section Headers */
.sidebar-section-header {
  padding: 12px 16px 6px;
  font-size: 0.7rem;
  font-weight: 600;
  color: var(--brand-gray-400);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  opacity: 0.7;
}

/* Quick Stats Section */
.sidebar-stats {
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  flex-shrink: 0;
}

.sidebar-stat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 0;
  transition: all var(--transition-base);
}

.sidebar-stat-item:hover {
  transform: translateX(2px);
}

.sidebar-stat-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
  color: var(--brand-gray-400);
}

.sidebar-stat-value {
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--brand-primary-900);
}

.sidebar-stat-value.success {
  color: var(--brand-success);
}

/* Collapsed State */
.sidebar-collapsed .sidebar-nav-item {
  padding: 12px;
  justify-content: center;
}

.sidebar-collapsed .sidebar-nav-item span,
.sidebar-collapsed .sidebar-ai-badge,
.sidebar-collapsed .sidebar-section-header {
  display: none;
}

.sidebar-collapsed .sidebar-stat-item {
  flex-direction: column;
  gap: 4px;
  text-align: center;
}

.sidebar-collapsed .sidebar-stat-label span {
  display: none;
}

/* Tooltip for Collapsed State */
.sidebar-tooltip {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 12px;
  padding: 8px 12px;
  background: var(--brand-gray-900);
  color: var(--brand-white);
  border-radius: var(--radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  white-space: nowrap;
  opacity: 0;
  visibility: hidden;
  pointer-events: none;
  transition: all var(--transition-fast);
  z-index: 50;
}

/* Icon button tooltips positioned above */
.sidebar-icon-button .sidebar-tooltip {
  left: 50%;
  top: auto;
  bottom: 100%;
  transform: translateX(-50%);
  margin-left: 0;
  margin-bottom: 8px;
}

.sidebar-icon-button .sidebar-tooltip::before {
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 6px solid transparent;
  border-top-color: var(--brand-gray-900);
  border-right-color: transparent;
}

.sidebar-tooltip::before {
  content: '';
  position: absolute;
  right: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: var(--brand-gray-900);
}

.sidebar-collapsed .sidebar-nav-item:hover .sidebar-tooltip,
.sidebar-icon-button:hover .sidebar-tooltip {
  opacity: 1;
  visibility: visible;
}

/* Sidebar Toggle Button */
.sidebar-toggle {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  color: var(--brand-gray-400);
}

.sidebar-toggle:hover {
  background: var(--brand-gray-100);
  color: var(--brand-primary-600);
}

.sidebar-toggle-icon {
  transition: transform var(--transition-base);
}

.sidebar-collapsed .sidebar-toggle-icon {
  transform: rotate(180deg);
}

/* Floating Expand Button */
.sidebar-expand-float {
  position: absolute;
  top: 20px;
  right: -12px;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--brand-white);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
  cursor: pointer;
  transition: all var(--transition-base);
  z-index: 10;
}

.sidebar-expand-float:hover {
  box-shadow: var(--shadow-md);
  transform: scale(1.1);
}

/* Smooth Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-10px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar-nav-item {
  animation: slideIn var(--transition-slow) ease-out;
  animation-fill-mode: both;
}

.sidebar-nav-item:nth-child(1) { animation-delay: 0ms; }
.sidebar-nav-item:nth-child(2) { animation-delay: 50ms; }
.sidebar-nav-item:nth-child(3) { animation-delay: 100ms; }
.sidebar-nav-item:nth-child(4) { animation-delay: 150ms; }
.sidebar-nav-item:nth-child(5) { animation-delay: 200ms; }
.sidebar-nav-item:nth-child(6) { animation-delay: 250ms; }

/* Navigation Wrapper */
.sidebar-nav-wrapper {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 1rem 0.75rem 1.25rem;
  min-height: 0; /* Important for Firefox */
  position: relative;
}

/* Scroll shadows */
.sidebar-nav-wrapper::before,
.sidebar-nav-wrapper::after {
  content: '';
  position: sticky;
  left: 0;
  right: 0;
  height: 20px;
  pointer-events: none;
  z-index: 1;
}

.sidebar-nav-wrapper::before {
  top: 0;
  background: linear-gradient(to bottom, var(--brand-white) 0%, transparent 100%);
}

.sidebar-nav-wrapper::after {
  bottom: 0;
  background: linear-gradient(to top, var(--brand-white) 0%, transparent 100%);
}

.sidebar-nav-wrapper::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav-wrapper::-webkit-scrollbar-track {
  background: var(--brand-gray-100);
  border-radius: 3px;
}

.sidebar-nav-wrapper::-webkit-scrollbar-thumb {
  background: var(--brand-gray-400);
  border-radius: 3px;
}

.sidebar-nav-wrapper::-webkit-scrollbar-thumb:hover {
  background: var(--brand-primary-600);
}

/* Profile Section */
.sidebar-profile {
  padding: 12px 16px;
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  background: var(--brand-white);
  flex-shrink: 0;
}

/* Icon Buttons */
.sidebar-icon-button {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.sidebar-icon-button:hover {
  background: var(--brand-gray-100);
}

.sidebar-icon-button-icon {
  width: 18px;
  height: 18px;
  color: var(--brand-gray-400);
  transition: color var(--transition-base);
}

.sidebar-icon-button:hover .sidebar-icon-button-icon {
  color: var(--brand-gray-900);
}

.sidebar-profile-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  border-radius: var(--radius-sm);
  transition: all var(--transition-base);
  cursor: pointer;
}

.sidebar-profile-content:hover {
  background: var(--brand-gray-100);
}

.sidebar-profile-avatar {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-full);
  background: var(--brand-primary-600);
  color: var(--brand-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.875rem;
}

.sidebar-profile-info {
  flex: 1;
}

.sidebar-profile-name {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--brand-gray-900);
}

.sidebar-profile-email {
  font-size: 0.75rem;
  color: var(--brand-gray-400);
}

/* Responsive */
@media (max-width: 1024px) {
  .modern-sidebar {
    position: fixed;
    inset: 0;
    width: 280px;
    z-index: 40;
  }
  
  .sidebar-overlay {
    position: fixed;
    inset: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 30;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-base);
  }
  
  .sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
  }
}