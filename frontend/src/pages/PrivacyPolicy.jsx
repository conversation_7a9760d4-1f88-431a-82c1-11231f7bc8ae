import React from 'react';
import { Link } from 'react-router-dom';

const PrivacyPolicy = () => {
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-brand-primary-600 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <span className="text-xl font-semibold text-slate-900">Brand Wisdom Solutions</span>
            </Link>
            <Link to="/auth" className="text-brand-primary-600 hover:text-brand-primary-700 font-medium">
              Back to Login
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white shadow rounded-lg p-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-8">Privacy Policy</h1>
          
          <div className="prose prose-slate max-w-none">
            <p className="text-slate-600 mb-6">
              <strong>Effective Date:</strong> January 1, 2025<br />
              <strong>Last Updated:</strong> January 1, 2025
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">1. Introduction</h2>
              <p className="text-slate-600 mb-4">
                Brand Wisdom Solutions ("we," "our," or "us"), a Google Partner Agency, operates the AdsAI 
                Campaign Management Platform (the "Service") exclusively for our team members. This Privacy 
                Policy explains how we collect, use, and protect information when you use our Service.
              </p>
              <p className="text-slate-600 mb-4">
                This Service is an internal tool designed for Brand Wisdom Solutions employees and authorized 
                contractors only and is not available to the general public.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">2. Information We Collect</h2>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">2.1 Account Information</h3>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Employee email addresses (brandwisdom.in domain)</li>
                <li>Names and job titles</li>
                <li>Login credentials (securely hashed)</li>
                <li>Access permissions and role assignments</li>
              </ul>

              <h3 className="text-xl font-semibold text-slate-900 mb-3">2.2 Google Ads Data</h3>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Client account information accessed through Google Ads API</li>
                <li>Campaign performance metrics</li>
                <li>Keywords and search terms data</li>
                <li>Budget and bidding information</li>
              </ul>

              <h3 className="text-xl font-semibold text-slate-900 mb-3">2.3 Usage Data</h3>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Platform access logs</li>
                <li>Feature usage patterns</li>
                <li>Performance metrics</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">3. How We Use Information</h2>
              <p className="text-slate-600 mb-4">We use the collected information to:</p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Provide access to the campaign management platform</li>
                <li>Manage and optimize Google Ads campaigns for our clients</li>
                <li>Generate performance reports and analytics</li>
                <li>Improve platform functionality and user experience</li>
                <li>Ensure security and prevent unauthorized access</li>
                <li>Comply with Google Ads API Terms of Service</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">4. Data Security</h2>
              <p className="text-slate-600 mb-4">
                We implement industry-standard security measures to protect all data:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>SSL/TLS encryption for all data transmission</li>
                <li>Encrypted storage of sensitive information</li>
                <li>Role-based access control (RBAC)</li>
                <li>Regular security audits and updates</li>
                <li>Secure API authentication using OAuth 2.0</li>
                <li>Compliance with Google Ads API security requirements</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">5. Data Sharing</h2>
              <p className="text-slate-600 mb-4">
                As an internal tool, we do not share platform data with third parties except:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>With Google Ads API as required for platform functionality</li>
                <li>With our infrastructure providers (Vercel, Railway, Supabase) for hosting</li>
                <li>As required by law or legal process</li>
              </ul>
              <p className="text-slate-600 mb-4">
                Client data accessed through the platform remains confidential and is only accessible to 
                authorized Brand Wisdom Solutions team members.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">6. Data Retention</h2>
              <p className="text-slate-600 mb-4">
                We retain data as follows:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Employee account data: Duration of employment plus 30 days</li>
                <li>Platform usage logs: 90 days</li>
                <li>Client campaign data: As per client agreements and Google Ads API policies</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">7. Employee Rights</h2>
              <p className="text-slate-600 mb-4">
                Brand Wisdom Solutions employees have the right to:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Access their account information</li>
                <li>Request correction of inaccurate data</li>
                <li>Receive training on proper platform usage</li>
                <li>Report security concerns or data issues</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">8. Compliance</h2>
              <p className="text-slate-600 mb-4">
                This platform complies with:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Google Ads API Terms of Service</li>
                <li>Google Cloud Platform Terms</li>
                <li>Applicable data protection regulations</li>
                <li>Industry best practices for data security</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">9. Contact Information</h2>
              <p className="text-slate-600 mb-4">
                For questions about this Privacy Policy or data practices, contact:
              </p>
              <div className="bg-slate-50 p-4 rounded-lg text-slate-600">
                <p><strong>Brand Wisdom Solutions</strong></p>
                <p>Email: <EMAIL></p>
                <p>Phone: +91 96 07 01 03 05</p>
                <p>Address: Office 119, Shoppers Orbit, Vishrantwadi, Pune, Maharashtra 411015, India</p>
              </div>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">10. Changes to This Policy</h2>
              <p className="text-slate-600 mb-4">
                We may update this Privacy Policy periodically. Changes will be communicated to all team 
                members via internal channels. Continued use of the platform after changes indicates 
                acceptance of the updated policy.
              </p>
            </section>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white mt-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-slate-500">
            <p>&copy; 2025 Brand Wisdom Solutions. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default PrivacyPolicy;