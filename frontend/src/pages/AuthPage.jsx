import React from 'react'
import { LoginForm } from '../components/auth/LoginForm.jsx'

export const AuthPage = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Brand Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Brand Wisdom Solutions
          </h1>
          <p className="text-gray-600">
            AdsAI Campaign Management Platform
          </p>
          <div className="mt-2">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
              Internal Tool - Employees Only
            </span>
          </div>
        </div>

        {/* Login Form Only */}
        <LoginForm />

        {/* Internal Tool Notice */}
        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            This is an internal tool for Brand Wisdom Solutions employees only. 
            Access credentials are provided by the IT department.
          </p>
          <p className="text-sm text-gray-600 mt-2">
            Need access? Contact{' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:text-blue-700 font-medium">
              <EMAIL>
            </a>
          </p>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>© 2025 Brand Wisdom Solutions. All rights reserved.</p>
        </div>
      </div>
    </div>
  )
}

export default AuthPage