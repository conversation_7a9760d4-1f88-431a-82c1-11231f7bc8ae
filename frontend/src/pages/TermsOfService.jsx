import React from 'react';
import { Link } from 'react-router-dom';

const TermsOfService = () => {
  return (
    <div className="min-h-screen bg-slate-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link to="/" className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-brand-primary-600 rounded-full flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <span className="text-xl font-semibold text-slate-900">Brand Wisdom Solutions</span>
            </Link>
            <Link to="/auth" className="text-brand-primary-600 hover:text-brand-primary-700 font-medium">
              Back to Login
            </Link>
          </div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="bg-white shadow rounded-lg p-8">
          <h1 className="text-3xl font-bold text-slate-900 mb-8">Terms of Service</h1>
          
          <div className="prose prose-slate max-w-none">
            <p className="text-slate-600 mb-6">
              <strong>Effective Date:</strong> January 1, 2025<br />
              <strong>Last Updated:</strong> January 1, 2025
            </p>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">1. Agreement to Terms</h2>
              <p className="text-slate-600 mb-4">
                By accessing the Brand Wisdom Solutions AdsAI Campaign Management Platform ("Service"), you 
                agree to be bound by these Terms of Service. This Service is exclusively for authorized Brand 
                Wisdom Solutions employees and contractors ("Users").
              </p>
              <p className="text-slate-600 mb-4">
                Access to this platform is restricted to internal use only. Brand Wisdom Solutions is a 
                Google Partner Agency managing 20+ client accounts through MCC 310-946-3592. Unauthorized 
                access is prohibited and may result in legal action.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">2. Use License and Restrictions</h2>
              <h3 className="text-xl font-semibold text-slate-900 mb-3">2.1 Permitted Use</h3>
              <p className="text-slate-600 mb-4">Users are granted a limited, non-transferable license to:</p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Access the platform for managing client Google Ads campaigns</li>
                <li>Generate reports and analytics for client accounts</li>
                <li>Utilize AI-powered optimization features</li>
                <li>Collaborate with team members on campaign management</li>
              </ul>

              <h3 className="text-xl font-semibold text-slate-900 mb-3">2.2 Prohibited Uses</h3>
              <p className="text-slate-600 mb-4">Users must NOT:</p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Share login credentials with unauthorized persons</li>
                <li>Access client data beyond assigned permissions</li>
                <li>Export or misuse client campaign data</li>
                <li>Attempt to bypass security measures</li>
                <li>Use the platform for personal Google Ads accounts</li>
                <li>Violate Google Ads API Terms of Service</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">3. Account Responsibilities</h2>
              <p className="text-slate-600 mb-4">Each user is responsible for:</p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Maintaining confidentiality of account credentials</li>
                <li>All activities conducted under their account</li>
                <li>Immediately reporting unauthorized access</li>
                <li>Complying with company data protection policies</li>
                <li>Proper handling of client confidential information</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">4. Client Data and Confidentiality</h2>
              <p className="text-slate-600 mb-4">
                All client data accessed through the platform is strictly confidential. Users must:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Access only assigned client accounts</li>
                <li>Not disclose client data to unauthorized parties</li>
                <li>Follow data retention and deletion policies</li>
                <li>Report any data breaches immediately</li>
                <li>Comply with client-specific confidentiality agreements</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">5. Google Ads API Compliance</h2>
              <p className="text-slate-600 mb-4">
                This platform utilizes Google Ads API. All users must comply with:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Google Ads API Terms of Service</li>
                <li>Google Ads policies and guidelines</li>
                <li>API rate limits and usage restrictions</li>
                <li>Data handling requirements specified by Google</li>
              </ul>
              <p className="text-slate-600 mb-4">
                Violations may result in suspension of API access for the entire organization.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">6. Intellectual Property</h2>
              <p className="text-slate-600 mb-4">
                The platform, including all software, designs, and documentation, is the exclusive property 
                of Brand Wisdom Solutions. Users may not:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Copy, modify, or distribute the platform code</li>
                <li>Reverse engineer any part of the system</li>
                <li>Create derivative works based on the platform</li>
                <li>Use platform features to develop competing tools</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">7. Service Availability</h2>
              <p className="text-slate-600 mb-4">
                While we strive for maximum uptime, Brand Wisdom Solutions does not guarantee:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Uninterrupted access to the platform</li>
                <li>Error-free operation</li>
                <li>Compatibility with all devices or browsers</li>
              </ul>
              <p className="text-slate-600 mb-4">
                Scheduled maintenance will be communicated in advance when possible.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">8. Limitation of Liability</h2>
              <p className="text-slate-600 mb-4">
                Brand Wisdom Solutions shall not be liable for:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Indirect or consequential damages</li>
                <li>Loss of data due to user error</li>
                <li>Third-party service interruptions (Google Ads API)</li>
                <li>Actions taken based on platform recommendations</li>
              </ul>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">9. Termination</h2>
              <p className="text-slate-600 mb-4">
                Access to the platform may be terminated:
              </p>
              <ul className="list-disc pl-6 text-slate-600 mb-4">
                <li>Upon termination of employment or contract</li>
                <li>For violation of these Terms of Service</li>
                <li>For security or compliance reasons</li>
                <li>At the discretion of Brand Wisdom Solutions management</li>
              </ul>
              <p className="text-slate-600 mb-4">
                Upon termination, users must cease all platform use and return any downloaded data.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">10. Modifications to Terms</h2>
              <p className="text-slate-600 mb-4">
                Brand Wisdom Solutions reserves the right to modify these Terms of Service. Users will be 
                notified of significant changes via internal communication channels. Continued use after 
                modifications constitutes acceptance.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">11. Governing Law</h2>
              <p className="text-slate-600 mb-4">
                These Terms shall be governed by the laws of India and the jurisdiction of Pune, Maharashtra, 
                without regard to conflict of law provisions.
              </p>
            </section>

            <section className="mb-8">
              <h2 className="text-2xl font-semibold text-slate-900 mb-4">12. Contact Information</h2>
              <p className="text-slate-600 mb-4">
                For questions regarding these Terms of Service:
              </p>
              <div className="bg-slate-50 p-4 rounded-lg text-slate-600">
                <p><strong>Brand Wisdom Solutions</strong></p>
                <p>Email: <EMAIL></p>
                <p>Phone: +91 96 07 01 03 05</p>
                <p>Address: Office 119, Shoppers Orbit, Vishrantwadi, Pune, Maharashtra 411015, India</p>
              </div>
            </section>

            <div className="mt-8 p-4 bg-amber-50 border border-amber-200 rounded-lg">
              <p className="text-amber-800 text-sm">
                <strong>Note:</strong> By using the Brand Wisdom Solutions Campaign Management Platform, 
                you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.
              </p>
            </div>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white mt-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="text-center text-sm text-slate-500">
            <p>&copy; 2025 Brand Wisdom Solutions. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default TermsOfService;