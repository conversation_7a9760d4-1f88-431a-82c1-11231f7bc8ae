import React, { useState } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../../contexts/AuthContext.jsx'
import { Button } from '../ui/button.jsx'
import { Input } from '../ui/input.jsx'
import { Label } from '../ui/label.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card.jsx'
import { Alert, AlertDescription } from '../ui/alert.jsx'
import { Loader2, Mail, Lock } from 'lucide-react'
import { loginSchema } from '../../lib/validations.js'

export const LoginForm = () => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  
  const { signIn } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validate form data
    const validation = loginSchema.safeParse({ email, password })
    
    if (!validation.success) {
      setError(validation.error.errors[0].message)
      setLoading(false)
      return
    }

    const { error: signInError } = await signIn(email, password)
    
    if (signInError) {
      setError(signInError.message)
      setLoading(false)
    } else {
      // Navigate to dashboard on successful login
      const from = location.state?.from?.pathname || '/dashboard'
      navigate(from, { replace: true })
    }
  }

  return (
    <Card className="auth-card">
      <CardHeader className="auth-header">
        <CardTitle className="auth-title">
          Welcome Back
        </CardTitle>
        <CardDescription className="auth-description">
          Sign in to your AdsAI Platform account
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="auth-form">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="form-group">
            <Label htmlFor="email" className="form-label">Email</Label>
            <div className="input-wrapper">
              <Mail className="input-icon" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="form-input with-icon"
                required
                disabled={loading}
              />
            </div>
          </div>
          
          <div className="form-group">
            <Label htmlFor="password" className="form-label">Password</Label>
            <div className="input-wrapper">
              <Lock className="input-icon" />
              <Input
                id="password"
                type="password"
                placeholder="Enter your password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="form-input with-icon"
                required
                disabled={loading}
              />
            </div>
          </div>
          
          <Button 
            type="submit" 
            variant="primary"
            className="btn-primary w-full" 
            disabled={loading}
            loading={loading}
          >
            Sign In
          </Button>
        </form>
        
        <div className="auth-footer">
          <p className="auth-footer-text text-center text-sm text-gray-600">
            This is an internal tool for Brand Wisdom Solutions employees only.
            <br />
            Access credentials are provided by the IT department.
          </p>
        </div>
      </CardContent>
    </Card>
  )
}