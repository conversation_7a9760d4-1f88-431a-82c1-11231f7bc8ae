import React, { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext.jsx'
import { Button } from '../ui/button.jsx'
import { Input } from '../ui/input.jsx'
import { Label } from '../ui/label.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../ui/card.jsx'
import { Alert, AlertDescription } from '../ui/alert.jsx'
import { Loader2, Mail, Lock, User } from 'lucide-react'

export const SignUpForm = ({ onToggleMode }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [fullName, setFullName] = useState('')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [success, setSuccess] = useState(false)
  
  const { signUp } = useAuth()

  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    const { error: signUpError } = await signUp(email, password, fullName)
    
    if (signUpError) {
      setError(signUpError.message)
    } else {
      setSuccess(true)
    }
    
    setLoading(false)
  }

  if (success) {
    return (
      <Card className="auth-card">
        <CardHeader className="auth-header">
          <CardTitle className="auth-title">
            Check Your Email
          </CardTitle>
          <CardDescription className="auth-description">
            We've sent you a confirmation link to complete your registration
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="auth-success">
            <Button onClick={onToggleMode} variant="secondary" className="btn-secondary w-full">
              Back to Sign In
            </Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="auth-card">
      <CardHeader className="auth-header">
        <CardTitle className="auth-title">
          Create Account
        </CardTitle>
        <CardDescription className="auth-description">
          Join the Google Ads AI Platform
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="auth-form">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="form-group">
            <Label htmlFor="fullName" className="form-label">Full Name</Label>
            <div className="input-wrapper">
              <User className="input-icon" />
              <Input
                id="fullName"
                type="text"
                placeholder="Enter your full name"
                value={fullName}
                onChange={(e) => setFullName(e.target.value)}
                className="form-input with-icon"
                required
                disabled={loading}
              />
            </div>
          </div>
          
          <div className="form-group">
            <Label htmlFor="email" className="form-label">Email</Label>
            <div className="input-wrapper">
              <Mail className="input-icon" />
              <Input
                id="email"
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="form-input with-icon"
                required
                disabled={loading}
              />
            </div>
          </div>
          
          <div className="form-group">
            <Label htmlFor="password" className="form-label">Password</Label>
            <div className="input-wrapper">
              <Lock className="input-icon" />
              <Input
                id="password"
                type="password"
                placeholder="Create a password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="form-input with-icon"
                required
                disabled={loading}
                minLength={6}
              />
            </div>
          </div>
          
          <Button 
            type="submit" 
            className="btn-primary w-full" 
            disabled={loading}
            loading={loading}
          >
            Create Account
          </Button>
        </form>
        
        <div className="auth-footer">
          <span className="auth-footer-text">Already have an account? </span>
          <button
            type="button"
            onClick={onToggleMode}
            className="btn-link"
          >
            Sign in
          </button>
        </div>
      </CardContent>
    </Card>
  )
}