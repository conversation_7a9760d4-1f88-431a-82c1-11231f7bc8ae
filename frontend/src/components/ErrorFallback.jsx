import React from 'react'
import { <PERSON><PERSON><PERSON>riangle, Refresh<PERSON><PERSON>, Home } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'

export const ErrorFallback = ({ error, resetErrorBoundary }) => {
  return (
    <div className="error-page">
      <Card className="error-fallback-card">
        <CardHeader className="error-fallback-header">
          <AlertTriangle className="error-fallback-icon" />
          <CardTitle className="error-fallback-title">
            Oops! Something went wrong
          </CardTitle>
        </CardHeader>
        <CardContent className="error-fallback-content">
          <p className="error-fallback-message">
            We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.
          </p>
          
          {error?.message && (
            <div className="error-message-box">
              <p className="error-message-text">{error.message}</p>
            </div>
          )}
          
          {import.meta.env.DEV && error?.stack && (
            <details className="error-details">
              <summary className="error-details-summary">
                Show error details
              </summary>
              <pre className="error-details-content">
                {error.stack}
              </pre>
            </details>
          )}
          
          <div className="error-fallback-actions">
            <Button
              onClick={resetErrorBoundary}
              className="btn-primary"
            >
              <RefreshCw className="button-icon" />
              Try Again
            </Button>
            <Button
              onClick={() => window.location.href = '/'}
              className="btn-secondary"
            >
              <Home className="button-icon" />
              Go Home
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export const logError = (error, errorInfo) => {
  // In production, you would send this to your error logging service
  console.error('Error caught by boundary:', error, errorInfo)
  
  // You could send to Sentry, LogRocket, etc.
  // if (window.Sentry) {
  //   window.Sentry.captureException(error, {
  //     contexts: {
  //       react: {
  //         componentStack: errorInfo.componentStack
  //       }
  //     }
  //   })
  // }
}