import React, { useState } from 'react'
import { But<PERSON> } from '../ui/button.jsx'
import { Input } from '../ui/input.jsx'
import { Label } from '../ui/label.jsx'
import { Alert } from '../ui/alert.jsx'
import { X, AlertCircle, CheckCircle } from 'lucide-react'
import { fetchWithAuth } from '../../lib/api.js'

export const CreateCampaignModal = ({ isOpen, onClose, accountId, accountName }) => {
  const [formData, setFormData] = useState({
    name: '',
    budget_amount: '',
    campaign_type: 'SEARCH'
  })
  
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState(null)
  
  const handleSubmit = async (e) => {
    e.preventDefault()
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetchWithAuth(
        `/api/google-ads/accounts/${accountId}/campaigns`,
        {
          method: 'POST',
          body: JSON.stringify({
            ...formData,
            budget_amount: parseFloat(formData.budget_amount)
          })
        }
      )
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        // Reset form on success
        setFormData({
          name: '',
          budget_amount: '',
          campaign_type: 'SEARCH'
        })
        
        // Close modal after 3 seconds if successful
        setTimeout(() => {
          onClose()
          setResult(null)
        }, 3000)
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Failed to create campaign',
        details: { error: error.message }
      })
    } finally {
      setLoading(false)
    }
  }
  
  if (!isOpen) return null
  
  return (
    <div className="modal-overlay">
      <div className="modal-content">
        <div className="modal-header">
          <h2 className="modal-title">Create Campaign</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        
        
        <form onSubmit={handleSubmit} className="modal-form">
          <div className="form-group">
            <Label htmlFor="account" className="form-label">Account</Label>
            <Input
              id="account"
              value={`${accountName} (${accountId})`}
              disabled
              className="form-input disabled"
            />
          </div>
          
          <div className="form-group">
            <Label htmlFor="name" className="form-label">Campaign Name</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="e.g., Summer Sale Campaign"
              required
              className="form-input"
            />
          </div>
          
          <div className="form-group">
            <Label htmlFor="budget" className="form-label">Daily Budget (USD)</Label>
            <Input
              id="budget"
              type="number"
              step="0.01"
              min="1"
              value={formData.budget_amount}
              onChange={(e) => setFormData({ ...formData, budget_amount: e.target.value })}
              placeholder="50.00"
              required
              className="form-input"
            />
          </div>
          
          <div className="form-group">
            <Label htmlFor="type" className="form-label">Campaign Type</Label>
            <select
              id="type"
              value={formData.campaign_type}
              onChange={(e) => setFormData({ ...formData, campaign_type: e.target.value })}
              className="form-select"
            >
              <option value="SEARCH">Search</option>
              <option value="DISPLAY">Display</option>
              <option value="SHOPPING">Shopping</option>
              <option value="VIDEO">Video</option>
            </select>
          </div>
          
          {result && (
            <Alert variant={result.success ? 'success' : 'destructive'} className="modal-alert">
              <div className="alert-content">
                {result.success ? (
                  <CheckCircle className="alert-icon success" />
                ) : (
                  <AlertCircle className="alert-icon error" />
                )}
                <div className="alert-body">
                  <p className="alert-title">
                    {result.message}
                  </p>
                  {result.details && (
                    <div className="alert-details">
                      {result.details.note && (
                        <p className="alert-note">{result.details.note}</p>
                      )}
                      {result.details.solution && (
                        <p className="alert-solution">{result.details.solution}</p>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </Alert>
          )}
          
          <div className="modal-footer">
            <Button
              type="submit"
              disabled={loading}
              className="btn-primary"
              loading={loading}
            >
              {loading ? 'Creating...' : 'Create Campaign'}
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={loading}
              className="btn-secondary"
            >
              Cancel
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}