import React from 'react'
import { useRouteError, <PERSON> } from 'react-router-dom'
import { AlertTriangle, Home } from 'lucide-react'

export function ErrorBoundary() {
  const error = useRouteError()
  
  // console.error('Route Error:', error)

  return (
    <div className="error-page">
      <div className="error-container">
        <div className="error-icon-wrapper">
          <AlertTriangle className="error-page-icon" />
        </div>
        
        <h1 className="error-page-title">
          Oops! Something went wrong
        </h1>
        
        <p className="error-page-message">
          {error?.message || 'An unexpected error occurred'}
        </p>
        
        {error?.stack && (
          <details className="error-details">
            <summary className="error-details-summary">
              Show error details
            </summary>
            <pre className="error-details-content">
              {error.stack}
            </pre>
          </details>
        )}
        
        <Link
          to="/"
          className="btn-primary error-page-button"
        >
          <Home className="button-icon" />
          Back to Dashboard
        </Link>
      </div>
    </div>
  )
}

export default ErrorBoundary