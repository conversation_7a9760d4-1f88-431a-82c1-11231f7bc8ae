import React from 'react'
import { Line<PERSON><PERSON>, Line, ResponsiveContainer } from 'recharts'

export const Sparkline = ({ data, color = '#10B981', height = 40, showTrend = true }) => {
  // Return empty if no data provided
  if (!data || data.length === 0) {
    return null
  }

  const trend = showTrend ? calculateTrend(data) : 0

  return (
    <div className="sparkline-container">
      <div className="sparkline-chart" style={{ height }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 5, right: 5, bottom: 5, left: 5 }}>
            <Line
              type="monotone"
              dataKey="value"
              stroke={color}
              strokeWidth={2}
              dot={false}
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
      {showTrend && (
        <div className={`sparkline-trend ${trend >= 0 ? 'positive' : 'negative'}`}>
          {trend >= 0 ? '+' : ''}{trend.toFixed(1)}%
        </div>
      )}
    </div>
  )
}

function calculateTrend(data) {
  if (!data || data.length < 2) return 0
  const first = data[0].value
  const last = data[data.length - 1].value
  return ((last - first) / first) * 100
}