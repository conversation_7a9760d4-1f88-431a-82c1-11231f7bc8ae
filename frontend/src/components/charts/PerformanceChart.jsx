import React from 'react'
import { LineChart, Line, AreaChart, Area, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card.jsx'

const CustomTooltip = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    return (
      <div className="chart-tooltip">
        <p className="tooltip-title">{label}</p>
        {payload.map((entry, index) => (
          <div key={index} className="tooltip-item" style={{ color: entry.color }}>
            <span className="tooltip-label">{entry.name}:</span>
            <span className="tooltip-value">{
              entry.name.includes('Spend') || entry.name.includes('Cost') 
                ? `$${entry.value.toLocaleString()}` 
                : entry.value.toLocaleString()
            }</span>
          </div>
        ))}
      </div>
    )
  }
  return null
}

export const PerformanceChart = ({ data, type = 'line', metrics = ['impressions', 'clicks'], title = 'Performance Overview' }) => {
  // Return empty if no data provided
  if (!data || data.length === 0) {
    return null
  }
  
  const metricConfig = {
    impressions: { color: '#3B82F6', name: 'Impressions' },
    clicks: { color: '#10B981', name: 'Clicks' },
    conversions: { color: '#F59E0B', name: 'Conversions' },
    spend: { color: '#EF4444', name: 'Ad Spend' },
    ctr: { color: '#8B5CF6', name: 'CTR %' },
    cpc: { color: '#EC4899', name: 'Avg. CPC' }
  }

  const renderChart = () => {
    switch (type) {
      case 'area':
        return (
          <AreaChart data={data}>
            <defs>
              {metrics.map((metric) => (
                <linearGradient key={metric} id={`gradient-${metric}`} x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={metricConfig[metric]?.color} stopOpacity={0.3}/>
                  <stop offset="95%" stopColor={metricConfig[metric]?.color} stopOpacity={0}/>
                </linearGradient>
              ))}
            </defs>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="circle"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric) => (
              <Area
                key={metric}
                type="monotone"
                dataKey={metric}
                name={metricConfig[metric]?.name}
                stroke={metricConfig[metric]?.color}
                fill={`url(#gradient-${metric})`}
                strokeWidth={2}
              />
            ))}
          </AreaChart>
        )
      
      case 'bar':
        return (
          <BarChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="rect"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric, index) => (
              <Bar
                key={metric}
                dataKey={metric}
                name={metricConfig[metric]?.name}
                fill={metricConfig[metric]?.color}
                radius={[4, 4, 0, 0]}
              />
            ))}
          </BarChart>
        )
      
      default: // line
        return (
          <LineChart data={data}>
            <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
            <XAxis 
              dataKey="date" 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
            />
            <YAxis 
              stroke="#6B7280"
              fontSize={11}
              tickLine={false}
              axisLine={{ stroke: '#E5E7EB' }}
              tickFormatter={(value) => value > 1000 ? `${(value / 1000).toFixed(0)}k` : value}
            />
            <Tooltip content={<CustomTooltip />} />
            <Legend 
              verticalAlign="top" 
              height={40}
              iconType="line"
              wrapperStyle={{ fontSize: '13px', paddingTop: '8px' }}
            />
            {metrics.map((metric) => (
              <Line
                key={metric}
                type="monotone"
                dataKey={metric}
                name={metricConfig[metric]?.name}
                stroke={metricConfig[metric]?.color}
                strokeWidth={2}
                dot={false}
                activeDot={{ r: 5 }}
              />
            ))}
          </LineChart>
        )
    }
  }

  return (
    <div className="dashboard-card">
      <div className="card-header">
        <h3 className="card-title">{title}</h3>
      </div>
      <div className="chart-container">
        <div className="chart-wrapper">
          <ResponsiveContainer width="100%" height="100%">
            {renderChart()}
          </ResponsiveContainer>
        </div>
      </div>
    </div>
  )
}

