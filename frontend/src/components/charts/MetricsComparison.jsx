import React from 'react'
import { RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, PieChart, Pie, Cell, ResponsiveContainer, Legend, Tooltip } from 'recharts'
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card.jsx'

const COLORS = ['#1E429F', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#EC4899', '#6366F1', '#14B8A6']

export const MetricsComparison = ({ data, type = 'radar', title = 'Campaign Comparison' }) => {
  // Return empty if no data provided
  if (!data || (Array.isArray(data) ? data.length === 0 : !data.metrics)) {
    return null
  }

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <div className="chart-tooltip">
          {payload.map((entry, index) => (
            <div key={index} className="tooltip-item" style={{ color: entry.color || COLORS[index % COLORS.length] }}>
              <span className="tooltip-label">{entry.name || entry.dataKey}:</span>
              <span className="tooltip-value">{
                typeof entry.value === 'number' ? entry.value.toLocaleString() : entry.value
              }</span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  const renderRadarChart = () => (
    <RadarChart data={data.metrics}>
      <PolarGrid 
        gridType="polygon" 
        radialLines={true}
        stroke="#E5E7EB"
      />
      <PolarAngleAxis 
        dataKey="metric" 
        tick={{ fontSize: 12 }}
        className="text-grey-700"
      />
      <PolarRadiusAxis 
        domain={[0, 100]}
        tick={{ fontSize: 10 }}
        tickCount={5}
        axisLine={false}
      />
      <Tooltip content={<CustomTooltip />} />
      <Legend 
        verticalAlign="top" 
        height={36}
        wrapperStyle={{ fontSize: '12px' }}
      />
      {data.campaigns.map((campaign, index) => (
        <Radar
          key={campaign}
          name={campaign}
          dataKey={campaign}
          stroke={COLORS[index % COLORS.length]}
          fill={COLORS[index % COLORS.length]}
          fillOpacity={0.3}
          strokeWidth={2}
        />
      ))}
    </RadarChart>
  )

  const renderPieChart = () => {
    const pieData = data.map((item, index) => ({
      ...item,
      color: COLORS[index % COLORS.length]
    }))

    return (
      <PieChart>
        <Pie
          data={pieData}
          cx="50%"
          cy="50%"
          labelLine={false}
          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
          outerRadius={100}
          fill="#8884d8"
          dataKey="value"
        >
          {pieData.map((entry, index) => (
            <Cell key={`cell-${index}`} fill={entry.color} />
          ))}
        </Pie>
        <Tooltip content={<CustomTooltip />} />
      </PieChart>
    )
  }

  return (
    <Card className="dashboard-card">
      <CardHeader className="card-header">
        <CardTitle className="card-title">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="chart-container">
          <div className="chart-wrapper lg">
            <ResponsiveContainer width="100%" height="100%">
              {type === 'radar' ? renderRadarChart() : renderPieChart()}
            </ResponsiveContainer>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}