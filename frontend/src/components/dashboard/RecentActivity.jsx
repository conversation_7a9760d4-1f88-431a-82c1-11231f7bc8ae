import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../ui/card.jsx'
import { 
  Activity, 
  TrendingUp, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Clock,
  Zap,
  DollarSign,
  Target
} from 'lucide-react'

export const RecentActivity = ({ activities = [] }) => {
  const getActivityIcon = (type) => {
    switch (type) {
      case 'optimization':
        return <TrendingUp className="activity-icon optimization" />
      case 'alert':
        return <AlertTriangle className="activity-icon alert" />
      case 'success':
        return <CheckCircle className="activity-icon success" />
      case 'info':
        return <Info className="activity-icon info" />
      default:
        return <Activity className="activity-icon" />
    }
  }

  const getActivityColor = (type) => {
    switch (type) {
      case 'optimization':
        return 'optimization'
      case 'alert':
        return 'alert'
      case 'success':
        return 'success'
      case 'info':
        return 'info'
      default:
        return 'info'
    }
  }

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now - date
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMs / 3600000)
    const diffDays = Math.floor(diffMs / 86400000)

    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins} minute${diffMins > 1 ? 's' : ''} ago`
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    
    return date.toLocaleDateString()
  }

  return (
    <div className="dashboard-card">
      <div className="card-header">
        <h3 className="card-title">
          <Activity className="card-icon" />
          Recent Activity
        </h3>
      </div>
      <div className="recent-activity-content">
        {activities.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon-wrapper sm">
              <Clock className="empty-state-icon sm" />
            </div>
            <p className="empty-state-title sm">No recent activity</p>
            <p className="empty-state-description">
              Your optimization activities will appear here
            </p>
          </div>
        ) : (
          <div className="activity-list">
            {activities.slice(0, 5).map((activity, index) => (
              <div 
                key={activity.id}
                className="activity-item"
              >
                <div className={`activity-icon-wrapper ${getActivityColor(activity.type)}`}>
                  {getActivityIcon(activity.type)}
                </div>
                <div className="activity-content">
                  <p className="activity-message">
                    {activity.message}
                  </p>
                  {activity.impact && (
                    <p className="activity-impact">
                      {activity.impact}
                    </p>
                  )}
                  <p className="activity-time">
                    <Clock className="activity-time-icon" />
                    {formatTimestamp(activity.timestamp)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}