import React from 'react'
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  <PERSON><PERSON>ointer,
  Eye,
  DollarSign,
  Activity,
  ArrowRight,
  Play,
  Pause,
  Square,
  ChevronRight
} from 'lucide-react'
import { Sparkline } from '../charts/Sparkline.jsx'

export const CampaignOverview = ({ campaigns = [] }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Play className="status-icon" />
      case 'paused':
        return <Pause className="status-icon" />
      case 'ended':
        return <Square className="status-icon" />
      default:
        return null
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'active'
      case 'paused':
        return 'warning'
      case 'ended':
        return 'inactive'
      default:
        return 'inactive'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <div className="dashboard-card">
      <div className="card-header">
        <div className="card-title">
          <BarChart3 className="card-icon" />
          Campaign Performance
        </div>
        <Button variant="ghost" size="sm" className="btn-ghost">
          View All
          <ArrowRight />
        </Button>
      </div>
      <div className="campaign-overview-content">
        {campaigns.length === 0 ? (
          <div className="empty-state">
            <div className="empty-state-icon-wrapper">
              <BarChart3 className="empty-state-icon" />
            </div>
            <h3 className="empty-state-title">No campaigns found</h3>
            <p className="empty-state-description">
              Connect your Google Ads account to see campaigns
            </p>
          </div>
        ) : (
          <div className="campaign-list">
            {campaigns.slice(0, 3).map((campaign) => (
              <div 
                key={campaign.id}
                className="campaign-item"
              >
                <div className="campaign-item-header">
                  <div className="campaign-item-info">
                    <h4 className="campaign-name">
                      {campaign.name}
                    </h4>
                    <div className="campaign-meta">
                      <span className={`status-badge ${getStatusColor(campaign.status)}`}>
                        {getStatusIcon(campaign.status)}
                        {campaign.status}
                      </span>
                      <span className="campaign-budget">
                        Budget: {formatCurrency(campaign.budget)}
                      </span>
                    </div>
                  </div>
                  <div className="quality-score">
                    <p className="quality-score-label">
                      Quality Score
                    </p>
                    <div className={`quality-score-value ${
                      campaign.quality_score >= 8 ? 'excellent' :
                      campaign.quality_score >= 6 ? 'good' :
                      'poor'
                    }`}>
                      {campaign.quality_score}/10
                    </div>
                  </div>
                </div>

                <div className="campaign-metrics">
                  <div className="campaign-metric">
                    <p className="campaign-metric-label">Spend</p>
                    <p className="campaign-metric-value">
                      {formatCurrency(campaign.spend)}
                    </p>
                  </div>

                  <div className="campaign-metric">
                    <p className="campaign-metric-label">Impressions</p>
                    <p className="campaign-metric-value">
                      {formatNumber(campaign.impressions)}
                    </p>
                  </div>

                  <div className="campaign-metric">
                    <p className="campaign-metric-label">Clicks</p>
                    <p className="campaign-metric-value">
                      {formatNumber(campaign.clicks)}
                    </p>
                  </div>

                  <div className="campaign-metric">
                    <p className="campaign-metric-label">CTR</p>
                    <p className={`campaign-metric-value ${
                      campaign.ctr > 2.5 ? 'metric-positive' : 'metric-negative'
                    }`}>
                      {campaign.ctr}%
                    </p>
                  </div>

                  <div className="campaign-metric">
                    <p className="campaign-metric-label">CPC</p>
                    <p className="campaign-metric-value">
                      ${campaign.cpc}
                    </p>
                  </div>
                </div>

                <div className="campaign-actions">
                  <Button size="sm" variant="secondary" className="btn-secondary btn-sm">
                    View Details
                  </Button>
                  <Button size="sm" variant="primary" className="btn-primary btn-sm">
                    Quick Optimize
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}