import React from 'react'
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  <PERSON><PERSON>ointer,
  Eye,
  DollarSign,
  Activity,
  ArrowRight,
  Play,
  Pause,
  Square,
  ChevronRight
} from 'lucide-react'
import { Sparkline } from '../charts/Sparkline.jsx'

export const CampaignOverview = ({ campaigns = [] }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'active':
        return <Play className="h-3 w-3" />
      case 'paused':
        return <Pause className="h-3 w-3" />
      case 'ended':
        return <Square className="h-3 w-3" />
      default:
        return null
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'active':
        return 'bg-emerald-100 text-emerald-700'
      case 'paused':
        return 'bg-amber-100 text-amber-700'
      case 'ended':
        return 'bg-slate-100 text-slate-700'
      default:
        return 'bg-slate-100 text-slate-600'
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount)
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  return (
    <div className="bg-white rounded-lg border border-slate-200">
      <div className="p-4 border-b border-slate-200 flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BarChart3 className="h-5 w-5 text-slate-600" />
          <h3 className="font-semibold text-slate-900">Campaign Performance</h3>
        </div>
        <Button variant="ghost" size="sm" className="text-primary-600 hover:text-primary-700 -mr-2">
          View All
          <ArrowRight className="h-4 w-4" />
        </Button>
      </div>
      <div className="p-4">
        {campaigns.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-20 h-20 bg-slate-100 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <BarChart3 className="h-10 w-10 text-slate-400" />
            </div>
            <p className="text-lg font-semibold text-slate-900 mb-2">No campaigns found</p>
            <p className="text-sm text-slate-600">
              Connect your Google Ads account to see campaigns
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {campaigns.slice(0, 3).map((campaign) => (
              <div 
                key={campaign.id}
                className="border-b border-slate-100 last:border-0 pb-4 last:pb-0"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="font-medium text-slate-900">
                      {campaign.name}
                    </h4>
                    <div className="flex items-center gap-3 mt-1">
                      <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(campaign.status)}`}>
                        {getStatusIcon(campaign.status)}
                        {campaign.status}
                      </span>
                      <span className="text-xs text-slate-500">
                        Budget: {formatCurrency(campaign.budget)}
                      </span>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-slate-500">
                      Quality Score
                    </p>
                    <div className={`text-xl font-semibold ${
                      campaign.quality_score >= 8 ? 'text-green-600' :
                      campaign.quality_score >= 6 ? 'text-amber-600' :
                      'text-red-600'
                    }`}>
                      {campaign.quality_score}/10
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-5 gap-4 mb-4">
                  <div>
                    <p className="text-xs text-slate-500">Spend</p>
                    <p className="font-medium text-sm text-slate-900 mt-0.5">
                      {formatCurrency(campaign.spend)}
                    </p>
                  </div>

                  <div>
                    <p className="text-xs text-slate-500">Impressions</p>
                    <p className="font-medium text-sm text-slate-900 mt-0.5">
                      {formatNumber(campaign.impressions)}
                    </p>
                  </div>

                  <div>
                    <p className="text-xs text-slate-500">Clicks</p>
                    <p className="font-medium text-sm text-slate-900 mt-0.5">
                      {formatNumber(campaign.clicks)}
                    </p>
                  </div>

                  <div>
                    <p className="text-xs text-slate-500">CTR</p>
                    <p className={`font-medium text-sm mt-0.5 ${
                      campaign.ctr > 2.5 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {campaign.ctr}%
                    </p>
                  </div>

                  <div>
                    <p className="text-xs text-slate-500">CPC</p>
                    <p className="font-medium text-sm text-slate-900 mt-0.5">
                      ${campaign.cpc}
                    </p>
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" variant="outline" className="flex-1">
                    View Details
                  </Button>
                  <Button size="sm" variant="primary" className="flex-1">
                    Quick Optimize
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}