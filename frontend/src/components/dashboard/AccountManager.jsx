import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  Building, 
  ChevronDown, 
  ChevronRight, 
  Users, 
  BarChart3,
  AlertCircle,
  ExternalLink,
  Settings,
  DollarSign,
  MousePointer,
  Eye,
  Activity,
  Plus
} from 'lucide-react'
import { useGoogleAdsAccounts } from '../../hooks/useGoogleAdsAccounts.js'
import { fetchWithAuth } from '../../lib/api.js'
import { CreateCampaignModal } from '../campaigns/CreateCampaignModal.jsx'

export const AccountManager = () => {
  const [expandedAccounts, setExpandedAccounts] = useState({})
  const [createCampaignModal, setCreateCampaignModal] = useState({
    isOpen: false,
    accountId: null,
    accountName: null,
    isTestAccount: false
  })
  const { accounts, loading, error, refetch } = useGoogleAdsAccounts()

  const toggleAccount = (accountId) => {
    setExpandedAccounts(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }))
  }

  const getAccountTypeIcon = (accountType) => {
    switch (accountType) {
      case 'MCC':
        return <Building className="h-5 w-5 text-primary-600" />
      case 'STANDARD':
        return <BarChart3 className="h-5 w-5 text-emerald-600" />
      default:
        return <Users className="h-5 w-5 text-slate-400" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const connectGoogleAds = async () => {
    try {
      // Get OAuth URL from backend
      const response = await fetchWithAuth('/api/google-ads/auth/url')
      if (!response.ok) {
        throw new Error('Failed to get OAuth URL')
      }
      
      const data = await response.json()
      
      // Redirect to Google OAuth
      window.location.href = data.auth_url
    } catch (error) {
      // Failed to start OAuth
      alert('Failed to connect Google Ads. Please try again.')
    }
  }

  // Show connect button if no accounts are loaded and not loading
  if (!loading && accounts.length === 0) {
    return (
      <Card className="dashboard-card">
        <CardHeader className="p-6">
          <CardTitle className="card-title text-xl">
            <Building className="h-6 w-6 text-primary-600" />
            Google Ads Setup Required
          </CardTitle>
          <CardDescription className="text-base mt-2">
            Connect your agency's Google Ads Manager account to access all client campaigns
          </CardDescription>
        </CardHeader>
        <CardContent className="p-6 pt-0 space-y-4">
          <Button 
            onClick={connectGoogleAds}
            className="w-full h-12 text-base font-medium bg-primary-600 hover:bg-primary-700"
          >
            <ExternalLink className="h-5 w-5 mr-2" />
            Connect Agency MCC Account
          </Button>
          <div className="text-sm text-slate-600 bg-slate-50 p-5 rounded-lg">
            <p className="font-semibold mb-3">First time setup:</p>
            <ol className="space-y-2 list-decimal list-inside">
              <li>Use your agency's Google Ads Manager (MCC) account</li>
              <li>Grant access to view and manage client accounts</li>
              <li>All team members will have access to connected accounts</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card className="dashboard-card">
        <CardContent className="p-12">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mr-3"></div>
            <span className="text-text">Loading accounts...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="dashboard-card error-card">
        <CardContent className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="font-medium">Failed to load accounts: {error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {accounts.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="h-12 w-12 text-slate-400 mx-auto mb-4" />
            <p className="text-lg font-semibold text-slate-900 mb-2">No Google Ads accounts found</p>
            <p className="text-sm text-slate-600">
              Make sure you have access to at least one Google Ads account
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {accounts.map((account) => (
            <Card 
              key={account.customer_id} 
              className="account-card"
            >
              <CardHeader 
                className="cursor-pointer hover:bg-slate-50 transition-colors p-5"
                onClick={() => toggleAccount(account.customer_id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {expandedAccounts[account.customer_id] ? 
                      <ChevronDown className="h-5 w-5 text-slate-600" /> : 
                      <ChevronRight className="h-5 w-5 text-slate-600" />
                    }
                    <div className="p-2.5 bg-slate-100 rounded-lg">
                      {getAccountTypeIcon(account.type)}
                    </div>
                    <div>
                      <CardTitle className="account-title">
                        {account.descriptive_name}
                      </CardTitle>
                      <div className="flex items-center gap-3 mt-1">
                        <span className="text-sm text-slate-600">
                          ID: {account.customer_id}
                        </span>
                        <span className={`px-2.5 py-1 rounded-md text-xs font-medium uppercase ${
                          account.type === 'MCC' 
                            ? 'bg-primary-600 text-white' 
                            : 'bg-emerald-100 text-emerald-700'
                        }`}>
                          {account.type}
                        </span>
                        {account.test_account && (
                          <span className="px-2.5 py-1 rounded-md text-xs font-medium uppercase bg-amber-100 text-amber-700">
                            TEST
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-sm text-slate-600 font-medium">
                        Monthly Spend
                      </p>
                      <p className="text-2xl font-bold text-slate-900">
                        {formatCurrency(account.metrics.spend)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardHeader>

              {expandedAccounts[account.customer_id] && (
                <CardContent className="border-t border-slate-200 bg-slate-50 p-5">
                  <div className="account-metrics">
                    <div className="metric-card">
                      <p className="text-sm text-slate-600 font-medium mb-2">Active Campaigns</p>
                      <p className="text-3xl font-bold text-slate-900">
                        {account.metrics.active_campaigns}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="text-sm text-slate-600 font-medium mb-2">Impressions</p>
                      <p className="text-3xl font-bold text-slate-900">
                        {formatNumber(account.metrics.impressions)}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="text-sm text-slate-600 font-medium mb-2">Clicks</p>
                      <p className="text-3xl font-bold text-slate-900">
                        {formatNumber(account.metrics.clicks)}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="text-sm text-slate-600 font-medium mb-2">CTR</p>
                      <p className="text-3xl font-bold text-slate-900">
                        {account.metrics.ctr}%
                      </p>
                    </div>
                  </div>

                  {account.sub_accounts && account.sub_accounts.length > 0 && (
                    <div className="mb-6">
                      <h4 className="text-base font-semibold text-slate-900 mb-4">
                        Sub-Accounts ({account.sub_accounts.length})
                      </h4>
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        {account.sub_accounts.map((subAccount) => (
                          <div 
                            key={subAccount.customer_id}
                            className="sub-account-card"
                          >
                            <div className="flex items-center justify-between mb-4">
                              <div className="flex items-center gap-3">
                                <div className="p-2 bg-slate-100 rounded-lg">
                                  <BarChart3 className="h-4 w-4 text-primary-600" />
                                </div>
                                <div>
                                  <p className="font-semibold text-base text-slate-900">
                                    {subAccount.descriptive_name}
                                  </p>
                                  <p className="text-sm text-slate-600">
                                    ID: {subAccount.customer_id} • {subAccount.active_campaigns} campaigns
                                  </p>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="font-bold text-lg text-slate-900">
                                  {formatCurrency(subAccount.spend)}
                                </p>
                                <p className="text-sm text-slate-500">monthly</p>
                              </div>
                            </div>
                            {subAccount.note && (
                              <p className="text-sm text-amber-700 bg-amber-50 px-3 py-2 rounded-md mb-4">
                                {subAccount.note}
                              </p>
                            )}
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => setCreateCampaignModal({
                                isOpen: true,
                                accountId: subAccount.customer_id,
                                accountName: subAccount.descriptive_name,
                                isTestAccount: subAccount.test_account
                              })}
                              className="w-full"
                            >
                              <Plus className="h-4 w-4" />
                              Create Campaign
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-3 pt-5 border-t border-slate-200">
                    <Button 
                      variant="outline"
                      size="default"
                      onClick={() => window.location.hash = `campaigns/${account.customer_id}`}
                      className="flex-1"
                    >
                      View Campaigns
                    </Button>
                    <Button 
                      variant="outline"
                      size="default"
                      onClick={() => window.location.hash = `analyze/${account.customer_id}`}
                      className="flex-1"
                    >
                      Analyze Search Terms
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
      
      <CreateCampaignModal
        isOpen={createCampaignModal.isOpen}
        onClose={() => setCreateCampaignModal({ ...createCampaignModal, isOpen: false })}
        accountId={createCampaignModal.accountId}
        accountName={createCampaignModal.accountName}
        isTestAccount={createCampaignModal.isTestAccount}
      />
    </div>
  )
}