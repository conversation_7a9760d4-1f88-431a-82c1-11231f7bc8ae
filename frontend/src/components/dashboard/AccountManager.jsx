import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '../ui/card.jsx'
import { Button } from '../ui/button.jsx'
import { 
  Building, 
  ChevronDown, 
  ChevronRight, 
  Users, 
  BarChart3,
  AlertCircle,
  ExternalLink,
  Settings,
  DollarSign,
  MousePointer,
  Eye,
  Activity,
  Plus
} from 'lucide-react'
import { useGoogleAdsAccounts } from '../../hooks/useGoogleAdsAccounts.js'
import { fetchWithAuth } from '../../lib/api.js'
import { CreateCampaignModal } from '../campaigns/CreateCampaignModal.jsx'

export const AccountManager = () => {
  const [expandedAccounts, setExpandedAccounts] = useState({})
  const [createCampaignModal, setCreateCampaignModal] = useState({
    isOpen: false,
    accountId: null,
    accountName: null
  })
  const { accounts, loading, error, refetch } = useGoogleAdsAccounts()

  const toggleAccount = (accountId) => {
    setExpandedAccounts(prev => ({
      ...prev,
      [accountId]: !prev[accountId]
    }))
  }

  const getAccountTypeIcon = (accountType) => {
    switch (accountType) {
      case 'MCC':
        return <Building className="account-type-icon" />
      case 'STANDARD':
        return <BarChart3 className="account-type-icon success" />
      default:
        return <Users className="account-type-icon" />
    }
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatNumber = (num) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M'
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K'
    }
    return num.toString()
  }

  const connectGoogleAds = async () => {
    try {
      // Get OAuth URL from backend
      const response = await fetchWithAuth('/api/google-ads/auth/url')
      if (!response.ok) {
        throw new Error('Failed to get OAuth URL')
      }
      
      const data = await response.json()
      
      // Redirect to Google OAuth
      window.location.href = data.auth_url
    } catch (error) {
      // Failed to start OAuth
      alert('Failed to connect Google Ads. Please try again.')
    }
  }

  // Show connect button if no accounts are loaded and not loading
  if (!loading && accounts.length === 0) {
    return (
      <Card className="dashboard-card">
        <CardHeader className="card-header">
          <CardTitle className="card-title">
            <Building className="card-icon" />
            Google Ads Setup Required
          </CardTitle>
          <CardDescription>
            Connect your agency's Google Ads Manager account to access all client campaigns
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button 
            onClick={connectGoogleAds}
            className="btn-primary w-full"
            size="lg"
          >
            <ExternalLink />
            Connect Agency MCC Account
          </Button>
          <div className="info-box mt-4">
            <p className="info-box-title">First time setup:</p>
            <ol className="info-list">
              <li>Use your agency's Google Ads Manager (MCC) account</li>
              <li>Grant access to view and manage client accounts</li>
              <li>All team members will have access to connected accounts</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (loading) {
    return (
      <Card className="dashboard-card">
        <CardContent>
          <div className="loading-state">
            <div className="loading-spinner"></div>
            <span>Loading accounts...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="dashboard-card error-card">
        <CardContent>
          <div className="error-message">
            <AlertCircle className="error-icon" />
            <span>Failed to load accounts: {error}</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="accounts-container">
      {accounts.length === 0 ? (
        <Card className="dashboard-card">
          <CardContent>
            <div className="empty-state">
              <Users className="empty-state-icon" />
              <h3 className="empty-state-title">No Google Ads accounts found</h3>
              <p className="empty-state-description">
                Make sure you have access to at least one Google Ads account
              </p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="accounts-list">
          {accounts.map((account) => (
            <Card 
              key={account.customer_id} 
              className="account-card"
            >
              <CardHeader 
                className="account-header"
                onClick={() => toggleAccount(account.customer_id)}
              >
                <div className="account-header-content">
                  <div className="account-header-left">
                    {expandedAccounts[account.customer_id] ? 
                      <ChevronDown className="chevron-icon" /> : 
                      <ChevronRight className="chevron-icon" />
                    }
                    <div className="account-icon-wrapper">
                      {getAccountTypeIcon(account.type)}
                    </div>
                    <div>
                      <CardTitle className="account-title">
                        {account.descriptive_name}
                      </CardTitle>
                      <div className="account-meta">
                        <span className="account-id">
                          ID: {account.customer_id}
                        </span>
                        <span className={`status-badge ${
                          account.type === 'MCC' 
                            ? 'badge-primary' 
                            : 'badge-success'
                        }`}>
                          {account.type}
                        </span>
                        {account.test_account && (
                          <span className="status-badge badge-warning">
                            TEST
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="account-header-right">
                    <div className="account-metric">
                      <p className="account-metric-label">
                        Monthly Spend
                      </p>
                      <p className="account-metric-value">
                        {formatCurrency(account.metrics.spend)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardHeader>

              {expandedAccounts[account.customer_id] && (
                <CardContent className="account-content-expanded">
                  <div className="account-metrics">
                    <div className="metric-card">
                      <p className="metric-label">Active Campaigns</p>
                      <p className="metric-value">
                        {account.metrics.active_campaigns}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="metric-label">Impressions</p>
                      <p className="metric-value">
                        {formatNumber(account.metrics.impressions)}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="metric-label">Clicks</p>
                      <p className="metric-value">
                        {formatNumber(account.metrics.clicks)}
                      </p>
                    </div>
                    <div className="metric-card">
                      <p className="metric-label">CTR</p>
                      <p className="metric-value">
                        {account.metrics.ctr}%
                      </p>
                    </div>
                  </div>

                  {account.sub_accounts && account.sub_accounts.length > 0 && (
                    <div className="sub-accounts-section">
                      <h4 className="sub-accounts-title">
                        Sub-Accounts ({account.sub_accounts.length})
                      </h4>
                      <div className="sub-accounts-grid">
                        {account.sub_accounts.map((subAccount) => (
                          <div 
                            key={subAccount.customer_id}
                            className="sub-account-card"
                          >
                            <div className="sub-account-header">
                              <div className="sub-account-info">
                                <div className="sub-account-icon">
                                  <BarChart3 />
                                </div>
                                <div>
                                  <p className="sub-account-name">
                                    {subAccount.descriptive_name}
                                  </p>
                                  <p className="sub-account-meta">
                                    ID: {subAccount.customer_id} • {subAccount.active_campaigns} campaigns
                                  </p>
                                </div>
                              </div>
                              <div className="sub-account-spend">
                                <p className="sub-account-spend-value">
                                  {formatCurrency(subAccount.spend)}
                                </p>
                                <p className="sub-account-spend-label">monthly</p>
                              </div>
                            </div>
                            {subAccount.note && (
                              <p className="sub-account-note">
                                {subAccount.note}
                              </p>
                            )}
                            <Button
                              size="sm"
                              variant="secondary"
                              onClick={() => setCreateCampaignModal({
                                isOpen: true,
                                accountId: subAccount.customer_id,
                                accountName: subAccount.descriptive_name
                              })}
                              className="btn-secondary w-full"
                            >
                              <Plus />
                              Create Campaign
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="account-actions">
                    <Button 
                      variant="secondary"
                      onClick={() => window.location.hash = `campaigns/${account.customer_id}`}
                      className="btn-secondary"
                    >
                      View Campaigns
                    </Button>
                    <Button 
                      variant="secondary"
                      onClick={() => window.location.hash = `analyze/${account.customer_id}`}
                      className="btn-secondary"
                    >
                      Analyze Search Terms
                    </Button>
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
      
      <CreateCampaignModal
        isOpen={createCampaignModal.isOpen}
        onClose={() => setCreateCampaignModal({ ...createCampaignModal, isOpen: false })}
        accountId={createCampaignModal.accountId}
        accountName={createCampaignModal.accountName}
      />
    </div>
  )
}