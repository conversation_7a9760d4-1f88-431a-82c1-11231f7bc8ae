import React from 'react'

// Simple alert components
export const Alert = React.forwardRef(({ className = '', variant = 'default', ...props }, ref) => {
  // Map variants to CSS classes
  const variantClasses = {
    default: 'alert',
    destructive: 'alert alert-error',
    success: 'alert alert-success',
    warning: 'alert alert-warning',
  }
  
  return (
    <div
      ref={ref}
      role="alert"
      className={`${variantClasses[variant] || variantClasses.default} ${className}`.trim()}
      {...props}
    />
  )
})
Alert.displayName = 'Alert'

export const AlertTitle = React.forwardRef(({ className = '', ...props }, ref) => (
  <h5
    ref={ref}
    className={`alert-title ${className}`.trim()}
    {...props}
  />
))
AlertTitle.displayName = 'AlertTitle'

export const AlertDescription = React.forwardRef(({ className = '', ...props }, ref) => (
  <div
    ref={ref}
    className={`alert-description ${className}`.trim()}
    {...props}
  />
))
AlertDescription.displayName = 'AlertDescription'