import React from 'react'
import { Loader2 } from 'lucide-react'

export const LoadingSpinner = ({ size = 'default', text = 'Loading...' }) => {
  const sizeClasses = {
    small: 'spinner-sm',
    default: 'spinner-md',
    large: 'spinner-lg'
  }

  return (
    <div className="loading-spinner-container">
      <Loader2 className={`loading-spinner ${sizeClasses[size]}`} />
      {text && <p className="loading-spinner-text">{text}</p>}
    </div>
  )
}

export const FullPageLoader = () => {
  return (
    <div className="full-page-loader">
      <div className="full-page-loader-content">
        <Loader2 className="loading-spinner spinner-lg" />
        <p className="full-page-loader-text">Loading AdsAI Platform...</p>
      </div>
    </div>
  )
}

export const RouteLoadingSpinner = () => {
  return (
    <div className="route-loading-spinner">
      <Loader2 className="loading-spinner spinner-md" />
    </div>
  )
}