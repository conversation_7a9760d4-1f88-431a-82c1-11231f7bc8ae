import React from 'react'
import { cn } from '../../lib/utils.js'

// Map variants to custom CSS classes
const buttonVariants = {
  default: 'btn-primary',
  primary: 'btn-primary',
  destructive: 'btn-danger',
  outline: 'btn-secondary',
  secondary: 'btn-secondary',
  ghost: 'btn-ghost',
  link: 'btn-link',
}

// Map sizes to custom CSS classes
const buttonSizes = {
  default: '',
  sm: 'btn-sm',
  lg: 'btn-lg',
  icon: 'btn-icon',
}

export const Button = React.forwardRef(
  ({ className, variant = 'default', size = 'default', disabled = false, loading = false, ...props }, ref) => {
    const sizeClass = buttonSizes[size]
    const variantClass = buttonVariants[variant]
    
    return (
      <button
        className={cn(
          variantClass,
          sizeClass,
          loading && 'btn-loading',
          className
        )}
        ref={ref}
        disabled={disabled || loading}
        {...props}
      >
        {loading && (
          <svg className="btn-spinner" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        )}
        {props.children}
      </button>
    )
  }
)

Button.displayName = 'Button'