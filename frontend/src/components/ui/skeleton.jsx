import React from 'react'
import { cn } from '../../lib/utils.js'

export const Skeleton = ({ className = '', ...props }) => {
  return (
    <div
      className={cn('skeleton-box', className)}
      {...props}
    />
  )
}

export const SkeletonCard = ({ lines = 3 }) => {
  return (
    <div className="dashboard-card">
      <div className="p-3 space-y-2">
        <div className="flex items-center justify-between">
          <Skeleton className="skeleton-title w-24" />
          <Skeleton className="skeleton-text w-12" />
        </div>
        <div className="space-y-1.5">
          {Array.from({ length: lines }).map((_, i) => (
            <Skeleton key={i} className={cn('skeleton-text', i === lines - 1 ? 'w-2/3' : 'w-full')} />
          ))}
        </div>
      </div>
    </div>
  )
}

export const SkeletonStats = () => {
  return (
    <div className="stat-card">
      <div className="flex items-center justify-between mb-1">
        <Skeleton className="skeleton-text w-20" />
        <Skeleton className="skeleton-text w-12" />
      </div>
      <Skeleton className="skeleton-title w-16" />
    </div>
  )
}

export const SkeletonTable = ({ rows = 5, columns = 4 }) => {
  return (
    <div className="data-table">
      {/* Header */}
      <div className="table-header">
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: columns }).map((_, i) => (
            <Skeleton key={i} className="skeleton-text w-24" />
          ))}
        </div>
      </div>
      {/* Rows */}
      <div className="table-body">
        {Array.from({ length: rows }).map((_, rowIndex) => (
          <div key={rowIndex} className="table-row">
            <div className="grid grid-cols-4 gap-4 p-4">
              {Array.from({ length: columns }).map((_, colIndex) => (
                <Skeleton 
                  key={colIndex} 
                  className={cn('skeleton-text', colIndex === 0 ? 'w-32' : 'w-20')} 
                />
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export const SkeletonChart = ({ height = 'h-48' }) => {
  return (
    <div className="dashboard-card">
      <div className="card-header">
        <Skeleton className="skeleton-title w-32" />
      </div>
      <div className="p-4">
        <div className={cn('skeleton-chart relative', height)}>
          <div className="absolute bottom-0 left-0 right-0 flex items-end justify-between px-2 pb-2 gap-1">
            {Array.from({ length: 7 }).map((_, i) => (
              <div 
                key={i} 
                className="skeleton-chart-bar"
                style={{ 
                  height: `${Math.random() * 60 + 20}%`,
                  animationDelay: `${i * 100}ms`
                }}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}