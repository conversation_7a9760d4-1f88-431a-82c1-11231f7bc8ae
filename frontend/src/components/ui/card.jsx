import React from 'react'

export const Card = React.forwardRef(({ className = '', ...props }, ref) => {
  return (
    <div
      ref={ref}
      className={`bg-white rounded-lg border border-slate-200 ${className}`}
      {...props}
    />
  )
})
Card.displayName = 'Card'

export const CardHeader = React.forwardRef(({ className = '', ...props }, ref) => (
  <div
    ref={ref}
    className={`p-4 border-b border-slate-200 ${className}`}
    {...props}
  />
))
CardHeader.displayName = 'CardHeader'

export const CardTitle = React.forwardRef(({ className = '', ...props }, ref) => (
  <h3
    ref={ref}
    className={`font-semibold text-slate-900 ${className}`}
    {...props}
  />
))
CardTitle.displayName = 'CardTitle'

export const CardDescription = React.forwardRef(({ className = '', ...props }, ref) => (
  <p
    ref={ref}
    className={`text-sm text-slate-600 ${className}`}
    {...props}
  />
))
CardDescription.displayName = 'CardDescription'

export const CardContent = React.forwardRef(({ className = '', ...props }, ref) => (
  <div
    ref={ref}
    className={`p-4 ${className}`}
    {...props}
  />
))
CardContent.displayName = 'CardContent'

export const CardFooter = React.forwardRef(({ className = '', ...props }, ref) => (
  <div
    ref={ref}
    className={`flex items-center p-4 border-t border-slate-200 ${className}`}
    {...props}
  />
))
CardFooter.displayName = 'CardFooter'