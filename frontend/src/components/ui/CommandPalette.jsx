import React, { useState, useEffect, useRef } from 'react'
import { 
  Search, 
  Command, 
  FileText, 
  BarChart3, 
  Users, 
  Settings,
  Zap,
  DollarSign,
  Target,
  Shield,
  Plus,
  Upload,
  Download,
  RefreshCw
} from 'lucide-react'
import { Card } from './card.jsx'

const commands = [
  // Quick Actions
  { 
    id: 'new-campaign', 
    label: 'Create New Campaign', 
    icon: Plus, 
    category: 'Quick Actions',
    shortcut: 'C',
    action: () => window.location.hash = 'campaigns/new'
  },
  { 
    id: 'analyze-search', 
    label: 'Analyze Search Terms', 
    icon: Search, 
    category: 'Quick Actions',
    shortcut: 'S',
    action: () => window.location.hash = 'search-terms'
  },
  { 
    id: 'generate-ad', 
    label: 'Generate Ad Copy', 
    icon: Zap, 
    category: 'Quick Actions',
    shortcut: 'A',
    action: () => window.location.hash = 'ad-generator'
  },
  
  // Navigation
  { 
    id: 'dashboard', 
    label: 'Go to Dashboard', 
    icon: BarChart3, 
    category: 'Navigation',
    action: () => window.location.hash = 'dashboard'
  },
  { 
    id: 'clients', 
    label: 'View All Clients', 
    icon: Users, 
    category: 'Navigation',
    action: () => window.location.hash = 'clients'
  },
  { 
    id: 'campaigns', 
    label: 'View All Campaigns', 
    icon: Target, 
    category: 'Navigation',
    action: () => window.location.hash = 'campaigns'
  },
  
  // Tools
  { 
    id: 'bulk-optimize', 
    label: 'Bulk Optimization', 
    icon: Zap, 
    category: 'Tools',
    action: () => window.location.hash = 'bulk-optimize'
  },
  { 
    id: 'negative-keywords', 
    label: 'Negative Keywords Manager', 
    icon: Shield, 
    category: 'Tools',
    action: () => window.location.hash = 'negative-keywords'
  },
  { 
    id: 'quality-scores', 
    label: 'Review Quality Scores', 
    icon: Target, 
    category: 'Tools',
    action: () => window.location.hash = 'quality-scores'
  },
  
  // Reports
  { 
    id: 'export-report', 
    label: 'Export Performance Report', 
    icon: Download, 
    category: 'Reports',
    shortcut: 'E',
    action: () => console.log('Export report')
  },
  { 
    id: 'import-data', 
    label: 'Import Campaign Data', 
    icon: Upload, 
    category: 'Reports',
    action: () => console.log('Import data')
  },
  
  // System
  { 
    id: 'refresh', 
    label: 'Refresh Dashboard', 
    icon: RefreshCw, 
    category: 'System',
    shortcut: 'R',
    action: () => window.location.reload()
  },
  { 
    id: 'settings', 
    label: 'Settings', 
    icon: Settings, 
    category: 'System',
    action: () => window.location.hash = 'settings'
  }
]

export const CommandPalette = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [search, setSearch] = useState('')
  const [selectedIndex, setSelectedIndex] = useState(0)
  const inputRef = useRef(null)

  // Filter commands based on search
  const filteredCommands = commands.filter(cmd => 
    cmd.label.toLowerCase().includes(search.toLowerCase()) ||
    cmd.category.toLowerCase().includes(search.toLowerCase())
  )

  // Group commands by category
  const groupedCommands = filteredCommands.reduce((acc, cmd) => {
    if (!acc[cmd.category]) acc[cmd.category] = []
    acc[cmd.category].push(cmd)
    return acc
  }, {})

  // Handle keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Open with Cmd/Ctrl + K
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setIsOpen(true)
      }
      
      // Close with Escape
      if (e.key === 'Escape' && isOpen) {
        setIsOpen(false)
      }
      
      // Navigate with arrow keys
      if (isOpen) {
        if (e.key === 'ArrowDown') {
          e.preventDefault()
          setSelectedIndex(prev => (prev + 1) % filteredCommands.length)
        } else if (e.key === 'ArrowUp') {
          e.preventDefault()
          setSelectedIndex(prev => (prev - 1 + filteredCommands.length) % filteredCommands.length)
        } else if (e.key === 'Enter' && filteredCommands[selectedIndex]) {
          e.preventDefault()
          filteredCommands[selectedIndex].action()
          setIsOpen(false)
        }
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [isOpen, selectedIndex, filteredCommands])

  // Focus input when opened
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
      setSearch('')
      setSelectedIndex(0)
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <div className="command-palette-overlay">
      <div 
        className="command-palette-backdrop"
        onClick={() => setIsOpen(false)}
      />
      
      <div className="command-palette-container">
        <Card className="command-palette-card">
          {/* Search Input */}
          <div className="command-palette-search">
            <Search className="command-palette-search-icon" />
            <input
              ref={inputRef}
              type="text"
              value={search}
              onChange={(e) => {
                setSearch(e.target.value)
                setSelectedIndex(0)
              }}
              placeholder="Type a command or search..."
              className="command-palette-input"
            />
            <kbd className="command-palette-kbd">
              ESC
            </kbd>
          </div>

          {/* Commands List */}
          <div className="command-palette-list">
            {filteredCommands.length === 0 ? (
              <div className="command-palette-empty">
                <p className="command-palette-empty-text">No commands found for "{search}"</p>
              </div>
            ) : (
              Object.entries(groupedCommands).map(([category, categoryCommands]) => (
                <div key={category}>
                  <div className="command-palette-category">
                    <p className="command-palette-category-title">
                      {category}
                    </p>
                  </div>
                  <div className="command-palette-group">
                    {categoryCommands.map((cmd, idx) => {
                      const globalIndex = filteredCommands.indexOf(cmd)
                      const isSelected = globalIndex === selectedIndex
                      
                      return (
                        <button
                          key={cmd.id}
                          onClick={() => {
                            cmd.action()
                            setIsOpen(false)
                          }}
                          onMouseEnter={() => setSelectedIndex(globalIndex)}
                          className={`command-palette-item ${
                            isSelected ? 'selected' : ''
                          }`}
                        >
                          <div className={`command-palette-item-icon ${
                            isSelected ? 'selected' : ''
                          }`}>
                            <cmd.icon className="command-icon" />
                          </div>
                          <span className="command-palette-item-label">
                            {cmd.label}
                          </span>
                          {cmd.shortcut && (
                            <kbd className={`command-palette-shortcut ${
                              isSelected ? 'selected' : ''
                            }`}>
                              ⌘{cmd.shortcut}
                            </kbd>
                          )}
                        </button>
                      )
                    })}
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Footer */}
          <div className="command-palette-footer">
            <div className="command-palette-footer-content">
              <div className="command-palette-footer-left">
                <span className="command-palette-footer-group">
                  <kbd className="command-palette-footer-kbd">↑</kbd>
                  <kbd className="command-palette-footer-kbd">↓</kbd>
                  Navigate
                </span>
                <span className="command-palette-footer-group">
                  <kbd className="command-palette-footer-kbd">↵</kbd>
                  Select
                </span>
              </div>
              <span className="command-palette-footer-group">
                <Command className="command-palette-footer-icon" />
                Press <kbd className="command-palette-footer-kbd">⌘K</kbd> anytime
              </span>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}