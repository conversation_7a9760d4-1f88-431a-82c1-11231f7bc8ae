import { lazy, Suspense } from 'react'
import { createBrowserRouter, Navigate } from 'react-router-dom'
import { ProtectedRoute } from './ProtectedRoute'
import { RouteLoadingSpinner, FullPageLoader } from '../components/ui/LoadingSpinner'
import ErrorBoundary from '../components/ErrorBoundary'

// Import public pages directly (no lazy loading for legal pages)
import PublicLoginPage from '../pages/PublicLoginPage'
import PrivacyPolicy from '../pages/PrivacyPolicy'
import TermsOfService from '../pages/TermsOfService'

// Define lazy routes using React Router 7 pattern
const DashboardLayout = lazy(() => import('../components/layout/DashboardLayout'))
const AuthPage = lazy(() => import('../pages/AuthPage'))

// Preload critical routes
export const preloadDashboard = () => import('../pages/DashboardPage')
export const preloadGoogleAds = () => import('../pages/GoogleAdsSetupPage')
export const preloadCampaigns = () => import('../pages/CampaignsPage')
export const preloadBidBudget = () => import('../pages/BidBudgetPage')
export const preloadReports = () => import('../pages/ReportsPage')
export const preloadMonitoring = () => import('../pages/MonitoringPage')
export const preloadKeywords = () => import('../pages/KeywordsPage')
export const preloadAuth = () => import('../pages/AuthPage')
export const preloadClients = () => import('../pages/ClientsPage')

// Preload AI features
export const preloadSearchMining = () => import('../pages/ai/SearchMiningPage')
export const preloadIntentClassifier = () => import('../pages/ai/IntentClassifierPage')
export const preloadAdCopyLab = () => import('../pages/ai/AdCopyLabPage')
export const preloadNegativeKeywords = () => import('../pages/ai/NegativeKeywordsPage')

// Wrap components in Suspense using React Router 7 pattern
const LazyRoute = ({ importFunc }) => {
  const LazyComponent = lazy(importFunc)
  return (
    <Suspense fallback={<RouteLoadingSpinner />}>
      <LazyComponent />
    </Suspense>
  )
}

// Create router configuration
export const router = createBrowserRouter([
  // Public routes (no authentication required)
  {
    path: '/',
    element: <PublicLoginPage />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/privacy',
    element: <PrivacyPolicy />,
    errorElement: <ErrorBoundary />
  },
  {
    path: '/terms',
    element: <TermsOfService />,
    errorElement: <ErrorBoundary />
  },
  // Protected dashboard routes
  {
    path: '/dashboard',
    element: (
      <ProtectedRoute>
        <Suspense fallback={<FullPageLoader />}>
          <DashboardLayout />
        </Suspense>
      </ProtectedRoute>
    ),
    errorElement: <ErrorBoundary />,
    children: [
      {
        index: true,
        element: <LazyRoute importFunc={() => import('../pages/DashboardPage')} />
      },
      {
        path: 'google-ads-setup',
        element: <LazyRoute importFunc={() => import('../pages/GoogleAdsSetupPage')} />
      },
      {
        path: 'clients',
        element: <LazyRoute importFunc={() => import('../pages/ClientsPage')} />
      },
      {
        path: 'onboarding',
        element: <LazyRoute importFunc={() => import('../components/onboarding/OnboardingFlow')} />
      },
      // Core Campaign Management Routes
      {
        path: 'campaigns',
        element: <LazyRoute importFunc={() => import('../pages/CampaignsPage')} />
      },
      {
        path: 'bid-budget',
        element: <LazyRoute importFunc={() => import('../pages/BidBudgetPage')} />
      },
      {
        path: 'reports',
        element: <LazyRoute importFunc={() => import('../pages/ReportsPage')} />
      },
      {
        path: 'monitoring',
        element: <LazyRoute importFunc={() => import('../pages/MonitoringPage')} />
      },
      {
        path: 'keywords',
        element: <LazyRoute importFunc={() => import('../pages/KeywordsPage')} />
      },
      // AI Feature Routes
      {
        path: 'ai',
        children: [
          {
            path: 'search-mining',
            element: <LazyRoute importFunc={() => import('../pages/ai/SearchMiningPage')} />
          },
          {
            path: 'intent-classifier',
            element: <LazyRoute importFunc={() => import('../pages/ai/IntentClassifierPage')} />
          },
          {
            path: 'ad-copy-lab',
            element: <LazyRoute importFunc={() => import('../pages/ai/AdCopyLabPage')} />
          },
          {
            path: 'negative-keywords',
            element: <LazyRoute importFunc={() => import('../pages/ai/NegativeKeywordsPage')} />
          },
          {
            path: 'bid-intelligence',
            element: <LazyRoute importFunc={() => import('../pages/ai/BidIntelligencePage')} />
          },
          {
            path: 'ad-extensions',
            element: <LazyRoute importFunc={() => import('../pages/ai/AdExtensionsPage')} />
          },
          {
            path: 'landing-page-synergy',
            element: <LazyRoute importFunc={() => import('../pages/ai/LandingPageSynergyPage')} />
          },
          {
            path: 'search-automation',
            element: <LazyRoute importFunc={() => import('../pages/ai/SearchAutomationPage')} />
          },
          {
            path: 'scripts-library',
            element: <LazyRoute importFunc={() => import('../pages/ai/ScriptsLibraryPage')} />
          },
          {
            path: 'insights-engine',
            element: <LazyRoute importFunc={() => import('../pages/ai/AIInsightsPage')} />
          }
        ]
      },
      // Settings Routes
      {
        path: 'settings',
        element: <LazyRoute importFunc={() => import('../pages/SettingsPage')} />
      },
      {
        path: 'team-settings',
        element: <LazyRoute importFunc={() => import('../pages/TeamSettingsPage')} />
      }
    ]
  },
  {
    path: '/auth',
    element: <LazyRoute importFunc={() => import('../pages/AuthPage')} />,
    errorElement: <ErrorBoundary />
  }
])