# AdsAI Platform Styling Guide

## Overview
This guide defines the styling approach for the AdsAI Platform, using a structured hybrid system that combines our custom CSS framework with Tailwind utilities.

## Core Principle: Custom CSS Framework First, Tailwind for Utilities

### Architecture
```
Custom CSS Framework (Primary)
├── theme.css        → CSS variables, global styles, typography
├── components.css   → Component classes (buttons, cards, forms)
├── sidebar.css      → Navigation-specific styles
└── alignment.css    → Layout fixes and alignments

Tailwind CSS (Utilities Only)
└── Used for: spacing, flexbox, grid, responsive, text alignment
```

## When to Use What

### Use Custom CSS Classes For:
- **Components**: buttons, cards, forms, navigation
- **Animations**: hover effects, transitions, loading states
- **Brand Elements**: colors, fonts, shadows, border radius
- **Complex Patterns**: dashboard cards, stat cards, activity items

### Use Tailwind Utilities For:
- **Layout**: flex, grid, positioning
- **Spacing**: margin, padding (p-4, m-2, gap-4)
- **Responsive**: sm:, md:, lg: breakpoints
- **Text Utilities**: text-center, font-bold, truncate
- **Simple States**: hidden, block, inline-flex

## Component Class Naming Conventions

### Buttons
```jsx
// Primary actions
<Button variant="primary" />  // → btn-primary

// Sizes
<Button size="sm" />          // → btn-sm
<Button size="lg" />          // → btn-lg
<Button size="icon" />        // → btn-icon

// States
<Button loading />            // → btn-loading
```

### Cards
```jsx
// Dashboard cards with animations
<Card />                      // → dashboard-card
<CardHeader />                // → card-header
<CardTitle />                 // → card-title
```

### Forms
```jsx
// Input elements
<Input />                     // → form-input
<Label />                     // → form-label
<Select />                    // → form-select
```

### Loading States
```jsx
// Skeleton components
<Skeleton />                  // → skeleton-box
<SkeletonText />             // → skeleton-text
<SkeletonTitle />            // → skeleton-title
```

## Color System

Always use CSS variables from theme.css:
```css
/* Brand Colors */
--brand-primary-*    /* Blue shades */
--brand-secondary    /* Dark gray */
--brand-gold-500     /* Accent gold */
--brand-success      /* Green */
--brand-error        /* Red */

/* Never use Tailwind color classes for brand colors */
❌ bg-blue-600
✅ background: var(--brand-primary-600)
```

## Practical Examples

### Creating a New Component

```jsx
// ❌ WRONG - Mixed approach
const Alert = () => (
  <div className="bg-white rounded-lg shadow-md p-4 border border-gray-200">
    <h3 className="text-lg font-semibold text-gray-900">Title</h3>
  </div>
)

// ✅ CORRECT - Custom class with utility helpers
const Alert = () => (
  <div className="alert-box">
    <h3 className="alert-title">Title</h3>
  </div>
)

// Then in components.css:
.alert-box {
  background: var(--brand-white);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
  padding: var(--space-4);
  border: 1px solid var(--border-color);
}

.alert-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--brand-gray-900);
}
```

### Combining Custom Classes with Utilities

```jsx
// ✅ CORRECT - Custom component class + layout utilities
<div className="dashboard-card flex items-center justify-between">
  <span className="stat-value">$45,231</span>
  <Icon className="w-5 h-5 text-gray-400" />
</div>

// The custom class handles styling, utilities handle layout
```

## Adding New Styles

### 1. Component Styles → components.css
```css
.new-component {
  /* Use theme variables */
  background: var(--brand-white);
  border-radius: var(--radius-md);
  /* Consistent with existing patterns */
}
```

### 2. Never Create Inline Tailwind Components
```jsx
// ❌ AVOID
style={{ backgroundColor: '#4172F5' }}

// ✅ USE
className="btn-primary"
```

### 3. Maintain Consistency
- Follow existing patterns in components.css
- Use theme variables for all values
- Keep animations under 300ms
- Test hover/focus states

## Migration Checklist

When updating existing components:
1. Check if custom CSS class exists
2. Replace Tailwind component classes with custom ones
3. Keep Tailwind utilities for layout only
4. Test all states (hover, focus, disabled, loading)
5. Verify animations work correctly

## Quick Reference

| Element | Custom Class | When to Use |
|---------|-------------|-------------|
| Primary Button | `btn-primary` | Main CTAs |
| Secondary Button | `btn-secondary` | Secondary actions |
| Dashboard Card | `dashboard-card` | Feature sections |
| Stat Card | `stat-card` | Metrics display |
| Form Input | `form-input` | All text inputs |
| Form Label | `form-label` | Input labels |
| Loading Box | `skeleton-box` | Loading states |

## Remember
- Custom CSS = Visual Design
- Tailwind = Layout Utilities
- Never mix both for the same purpose
- Always check components.css first