{"name": "brand-wisdom-campaigns", "framework": "vite", "buildCommand": "npm run build", "outputDirectory": "dist", "installCommand": "npm install", "routes": [{"handle": "filesystem"}, {"src": "/(.*)", "dest": "/index.html"}], "env": {"VITE_SUPABASE_URL": "@supabase_url", "VITE_SUPABASE_ANON_KEY": "@supabase_anon_key", "VITE_API_URL": "@api_url"}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}]}