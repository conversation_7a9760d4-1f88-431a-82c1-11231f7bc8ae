# UI/UX Patterns: AdsAI Platform

## Design System Overview

### Brand Identity
- **Primary Color**: #4172F5 (Brand Blue)
- **Success**: #27C084 (Brand Green)
- **Error**: #EF5E5E (Brand Red)
- **Dark**: #07153F (Brand Dark)
- **Typography**: <PERSON><PERSON> (primary), Playfair Display (headings)

### CSS Architecture
```
Custom CSS Framework (Primary)
├── theme.css        → Brand colors, variables
├── components.css   → UI components
├── sidebar.css      → Navigation
└── alignment.css    → Layout fixes

Tailwind CSS (Utilities only)
└── For: spacing, flexbox, grid, responsive
```

## Component Patterns

### Navigation Sidebar
- **Class**: `modern-sidebar`
- **Features**: 
  - Scrollable with custom scrollbar
  - Icon-only footer section
  - AI badges for AI features
  - Tooltips in collapsed state
  - Smooth hover animations

### Dashboard Cards
- **Class**: `dashboard-card`
- **Features**:
  - Hover lift effect
  - Consistent padding (1rem)
  - Border: 1px solid #E5E7EB
  - Shadow on hover
  - Border radius: 0.75rem

### Buttons
```css
Primary: btn-primary (blue background)
Secondary: btn-secondary (gray background)
Ghost: btn-ghost (transparent)
Sizes: default (h-10), sm (h-8), lg (h-11), icon (h-9 w-9)
```

### Forms
- **Input**: `form-input` with focus states
- **Label**: `form-label` with proper typography
- **Select**: `form-select` with consistent styling

### Loading States
- **Skeleton**: `skeleton-box`, `skeleton-text`
- **Spinner**: `loading-spinner` with size variants
- **Smooth animations**: All under 300ms

## Layout Patterns

### Dashboard Layout
```jsx
<div className="flex h-screen bg-slate-50">
  <aside className="modern-sidebar">...</aside>
  <main className="flex-1 overflow-auto">
    <header className="dashboard-header">...</header>
    <div className="p-6">
      {/* Content with consistent spacing */}
    </div>
  </main>
</div>
```

### Card Grid
```jsx
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
  <div className="dashboard-card">...</div>
</div>
```

## Interaction Patterns

### Hover Effects
- Cards: Lift with shadow increase
- Buttons: Darken with translateY(-1px)
- Navigation: Background color change
- Links: Underline or color change

### Transitions
- All transitions: 150-300ms
- Easing: ease-in-out
- Transform for movement
- Opacity for fades

### Focus States
- Blue ring with offset
- Clear keyboard navigation
- Accessible contrast ratios

## Responsive Patterns
- Mobile-first approach
- Breakpoints: sm (640px), md (768px), lg (1024px)
- Collapsible sidebar on mobile
- Stack cards vertically on small screens

## Best Practices
1. Use custom CSS classes for components
2. Use Tailwind only for utilities
3. Maintain consistent spacing (4px grid)
4. Keep animations subtle and fast
5. Ensure accessibility compliance
6. Test on multiple screen sizes