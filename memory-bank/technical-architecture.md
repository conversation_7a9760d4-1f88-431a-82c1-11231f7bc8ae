# Technical Architecture: AdsAI Platform

## Current Stack (Simplified MVP)

### Frontend
```json
{
  "framework": "React 19.1.0 + Vite 6.3.5",
  "language": "JavaScript (TypeScript removed for simplicity)",
  "styling": "Tailwind CSS 3.4.0 (CDN) + Custom CSS Framework",
  "icons": "Lucide React 0.515.0",
  "charts": "Recharts 2.15.3",
  "auth": "Supabase JS 2.49.9",
  "http": "Axios 1.9.0"
}
```

### Backend
```python
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Google Ads Integration
google-ads==22.1.0
google-auth-oauthlib==1.1.0

# Database
supabase  # Direct client usage (no ORM)
```

### Infrastructure
- **Database**: Supabase PostgreSQL (ID: irftzijnouubcjkyeuxj)
- **Tables**: 57 tables fully deployed
- **Auth**: Supabase Auth with JWT tokens
- **Storage**: Supabase Storage for assets

## Architecture Decisions
- **Single File Backend**: All routes in main.py (~450 lines)
- **No ORM**: Direct Supabase client for simplicity
- **No TypeScript**: Pure JavaScript for faster development
- **Minimal Dependencies**: 80% reduction from original
- **Build Time**: 1.2s (was 5+ seconds)

## Development Environment
```bash
# Frontend
cd frontend && npm run dev  # localhost:5173

# Backend (WSL/Linux)
cd backend
source venv/bin/activate
python3 run_server.py      # localhost:8000
```

## CSS Architecture
```
Custom CSS Framework (Primary)
├── theme.css        → CSS variables, Brand Wisdom colors
├── components.css   → Buttons, cards, forms
├── sidebar.css      → Navigation styles
└── alignment.css    → Layout fixes

Tailwind CSS (Utilities Only)
└── For: spacing, flexbox, grid, responsive
```

## Google Ads API Configuration
- **Developer Token**: USJoZ_CN_pYY2MP-jIhjqA (Basic Access)
- **MCC ID**: 310-946-3592 (3109463592 without dashes)
- **Daily Limits**: 15,000 operations/day
- **OAuth Scopes**: https://www.googleapis.com/auth/adwords

## Deployment Strategy
- **Frontend**: Vercel (static hosting)
- **Backend**: Railway (containerized)
- **Database**: Supabase (already production)
- **Domain**: campaigns.brandwisdom.in (recommended)