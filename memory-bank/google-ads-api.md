# Google Ads API Integration

## API Access Status
- **Status**: ✅ Basic Access APPROVED (January 6, 2025)
- **Developer Token**: USJoZ_CN_pYY2MP-jIhjqA
- **MCC Account**: 310-946-3592
- **Daily Limits**: 15,000 operations/day
- **Access Level**: Production (not test)
- **Platform Email**: <EMAIL>
- **Manager Email**: <EMAIL>
- **Test User Email**: <EMAIL> (currently authorized OAuth user)

## OAuth 2.0 Configuration
```json
{
  "client_id": "**********************************************.apps.googleusercontent.com",
  "client_secret": "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",
  "redirect_uri": "http://localhost:8000/auth/callback",
  "scopes": ["https://www.googleapis.com/auth/adwords"]
}
```

## Required API Services
1. **GoogleAdsService** - Account and campaign data
2. **SearchTermViewService** - Search query analysis
3. **KeywordPlanService** - Keyword research
4. **CampaignService** - Campaign management
5. **AdService** - Ad creation and optimization
6. **AdGroupService** - Ad group management
7. **BiddingStrategyService** - Bid optimization
8. **ExtensionFeedItemService** - Ad extensions
9. **SharedSetService** - Negative keyword lists
10. **BatchJobService** - Bulk operations
11. **ReportingService** - Custom reports

## Common Issues & Solutions

### Customer ID Format
```python
# ❌ Wrong
login_customer_id = "310-946-3592"  # With dashes

# ✅ Correct
login_customer_id = "**********"    # Without dashes
```

### Token Management
- Tokens stored in `user_google_ads_tokens` table
- Automatic refresh handling required
- Secure storage with encryption recommended

### Error Handling
- **PERMISSION_DENIED**: User lacks MCC access
- **GRPC_ERROR**: Test account with production token
- **QUOTA_EXCEEDED**: Daily limit reached

## Documentation Structure
```
/google-ads-docs/
├── auth-and-setup/       # OAuth flow, token management
├── campaigns/            # All campaign types
├── keywords-and-targeting/
├── reporting/            # GAQL queries
├── search-optimization/  # Quality Score, bidding
├── ads-and-extensions/
├── manager-accounts/     # MCC management
└── error-handling/       # Production error handling
```

## Testing Checklist
- [ ] OAuth user has MCC access (check in Google Ads UI)
- [ ] Developer token is production (not test)
- [ ] Customer IDs have no dashes
- [ ] Refresh token is valid
- [ ] API calls include login_customer_id