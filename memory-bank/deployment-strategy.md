# Deployment Strategy: AdsAI Platform

## Production Deployment Plan

### Frontend Deployment (Vercel)
```bash
# Build command
npm run build

# Output directory
dist/

# Environment variables needed
VITE_API_URL=https://api.campaigns.brandwisdom.in
VITE_SUPABASE_URL=https://irftzijnouubcjkyeuxj.supabase.co
VITE_SUPABASE_ANON_KEY=<production-key>
```

### Backend Deployment (Railway)
```python
# Start command
uvicorn app.main:app --host 0.0.0.0 --port $PORT

# Environment variables needed
GOOGLE_ADS_DEVELOPER_TOKEN=USJoZ_CN_pYY2MP-jIhjqA
GOOGLE_ADS_LOGIN_CUSTOMER_ID=3109463592
GOOGLE_ADS_CLIENT_ID=<production-oauth-id>
GOOGLE_ADS_CLIENT_SECRET=<production-oauth-secret>
SUPABASE_URL=<production-url>
SUPABASE_KEY=<production-service-key>
```

### Recommended Subdomains
1. **campaigns.brandwisdom.in** - Clean, professional
2. **adsai.brandwisdom.in** - Brand-focused
3. **dashboard.brandwisdom.in** - Generic but clear

## Pre-Deployment Checklist

### Security
- [ ] Update OAuth redirect URIs to production domain
- [ ] Generate new SECRET_KEY for production
- [ ] Enable HTTPS only
- [ ] Set up CORS for production domain
- [ ] Implement rate limiting
- [ ] Add security headers

### Database
- [ ] Enable Row Level Security on all tables
- [ ] Create production Supabase project (optional)
- [ ] Set up automated backups
- [ ] Test token encryption

### API Configuration
- [ ] Update Google Cloud Console with production URLs
- [ ] Verify developer token in production
- [ ] Test OAuth flow with production domain
- [ ] Monitor API usage limits

### Testing
- [ ] Complete OAuth flow test
- [ ] Multi-account access verification
- [ ] Token refresh testing
- [ ] Error scenario testing
- [ ] Performance testing

## Google Cloud API Verification Requirements

### Required Pages (Already Created)
1. **Homepage** (`/`) - Public login page
2. **Privacy Policy** (`/privacy`) - Comprehensive policy
3. **Terms of Service** (`/terms`) - Internal tool terms

### Verification Steps
1. Deploy to production domain
2. Submit application in Google Cloud Console
3. Include privacy policy and terms URLs
4. Specify internal tool usage
5. Use <EMAIL> as developer contact email
6. Wait for Google approval (usually 24-48 hours)

## Post-Deployment

### Monitoring
- Frontend: Vercel Analytics
- Backend: Railway metrics
- Errors: Sentry integration (planned)
- API Usage: Google Ads API Center

### Maintenance
- Regular dependency updates
- API usage monitoring
- Performance optimization
- Security audits

### Scaling Considerations
- Frontend: CDN via Vercel Edge Network
- Backend: Horizontal scaling on Railway
- Database: Supabase auto-scaling
- Caching: Redis for frequent data (future)